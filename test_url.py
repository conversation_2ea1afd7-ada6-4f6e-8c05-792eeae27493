from playwright.sync_api import sync_playwright
import time

with sync_playwright() as p:
    browser = p.chromium.launch(headless=False)
    page = browser.new_page()
    page.goto("https://www.linkedin.com/search/results/content/?datePosted=%22past-24h%22&keywords=KYC&origin=FACETED_SEARCH&sortBy=%22date_posted%22")
    print("<PERSON><PERSON><PERSON> opened. Please check if you can see search results.")
    time.sleep(30)
    browser.close()

