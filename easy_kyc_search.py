#!/usr/bin/env python3
"""
Easy LinkedIn KYC Search - No password input required
"""

from linkedin_keyword_scraper import LinkedInKeywordScraper
import json

def easy_kyc_search():
    print("🔍 Easy LinkedIn KYC Search")
    print("=" * 40)
    print("This will open a browser for manual login, then search for KYC posts.")
    print()
    
    try:
        with LinkedInKeywordScraper(
            headless=False,
            max_posts=25,
            require_login=True,
            email=None,  # No auto-login
            password=None
        ) as scraper:
            
            print("Searching for KYC posts...")
            posts = scraper.search_posts_by_keywords(["KYC"], hours_back=24)
            
            print(f"\n🎯 Results: Found {len(posts)} posts")
            
            if posts:
                print("\n📝 Sample posts:")
                for i, post in enumerate(posts[:3]):
                    print(f"\n{i+1}. Author: {post.get('author_name', 'Unknown')}")
                    print(f"   Date: {post.get('date', 'Unknown')}")
                    content = post.get('content', '')
                    preview = content[:150] + "..." if len(content) > 150 else content
                    print(f"   Content: {preview}")
                
                # Save results
                with open("kyc_results.json", "w") as f:
                    json.dump(posts, f, indent=2)
                print(f"\n💾 All results saved to kyc_results.json")
            else:
                print("\n❌ No posts found. This could be due to:")
                print("   - LinkedIn blocking automated access")
                print("   - No recent KYC posts in the last 24 hours")
                print("   - Search restrictions for your account")
                
    except Exception as e:
        print(f"\n❌ Error: {e}")

if __name__ == "__main__":
    easy_kyc_search()
