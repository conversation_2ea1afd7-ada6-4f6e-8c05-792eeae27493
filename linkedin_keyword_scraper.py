#!/usr/bin/env python3
"""
LinkedIn Keyword Post Scraper - No-login scraper for LinkedIn posts by keywords
"""

import logging
import random
import re
import time
from datetime import datetime, timedelta
# Removed typing imports for Python compatibility
from urllib.parse import quote_plus, urljoin
import json

from playwright.sync_api import sync_playwright, <PERSON>, <PERSON><PERSON><PERSON>, BrowserContext

logger = logging.getLogger(__name__)

class LinkedInKeywordScraper:
    """
    A no-login LinkedIn scraper that searches for posts containing specific keywords
    within the last 24 hours, implementing safety measures to avoid detection.
    """

    # LinkedIn URLs
    BASE_URL = "https://www.linkedin.com"
    SEARCH_URL = f"{BASE_URL}/search/results/content/"

    # Enhanced safety parameters for logged-in users
    MIN_DELAY = 5  # Increased minimum delay between actions
    MAX_DELAY = 15  # Increased maximum delay between actions
    SCROLL_DELAY = 2.0  # Slower scrolling to appear more human
    SCROLL_STEP = 300  # Smaller scroll steps
    MAX_POSTS_PER_KEYWORD = 25  # Reduced to be more conservative
    SEARCH_DELAY = 30  # Delay between different keyword searches
    MAX_DAILY_SEARCHES = 10  # Maximum searches per day to avoid flagging

    def __init__(self, headless=False, max_posts=25, require_login=True, email=None, password=None):
        """
        Initialize the LinkedIn keyword scraper with enhanced safety for logged-in users.

        Args:
            headless: Whether to run the browser in headless mode (False recommended for login)
            max_posts: Maximum number of posts to collect per keyword (reduced for safety)
            require_login: Whether to require login for better data access
            email: LinkedIn email for auto-login (optional)
            password: LinkedIn password for auto-login (optional)
        """
        self.headless = headless
        self.max_posts = max_posts
        self.require_login = require_login
        self.email = email
        self.password = password
        self.playwright = None
        self.browser = None
        self.context = None
        self.page = None
        self.collected_posts = set()  # Track collected post URLs to avoid duplicates
        self.is_logged_in = False
        self.search_count = 0  # Track number of searches in this session

    def __enter__(self):
        """Context manager entry point."""
        self.start()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit point."""
        self.close()

    def start(self):
        """Start the browser with enhanced safety measures."""
        logger.info("Starting browser for LinkedIn scraping with enhanced safety measures")

        # Initialize Playwright
        self.playwright = sync_playwright().start()

        # Maximum stealth browser arguments
        browser_args = [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--disable-gpu',
            '--disable-features=VizDisplayCompositor'
        ]

        # Launch browser
        try:
            logger.info("Launching Chrome...")
            self.browser = self.playwright.chromium.launch(
                headless=self.headless,
                args=browser_args,
                channel="chrome"
            )
            logger.info("Chrome launched successfully")
        except Exception as e:
            logger.warning(f"Failed to launch Chrome: {e}")
            logger.info("Launching Chromium...")
            self.browser = self.playwright.chromium.launch(
                headless=self.headless,
                args=browser_args
            )
            logger.info("Chromium launched successfully")

        # Create context with highly realistic settings
        self.context = self.browser.new_context(
            viewport={'width': 1440, 'height': 900},
            user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            locale='en-US',
            timezone_id='America/New_York',
            has_touch=False,
            is_mobile=False,
            java_script_enabled=True,
            accept_downloads=False,
            ignore_https_errors=False,  # More realistic
            extra_http_headers={
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Cache-Control': 'max-age=0',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-User': '?1',
                'Upgrade-Insecure-Requests': '1'
            }
        )

        # Add stealth scripts
        self._add_stealth_scripts()

        # Create page
        self.page = self.context.new_page()

        # Navigate to LinkedIn homepage first
        logger.info("Navigating to LinkedIn homepage...")
        self.page.goto(self.BASE_URL, wait_until='domcontentloaded')
        self._random_delay(min_delay=3, max_delay=6)

        # Handle login if required
        if self.require_login:
            self._handle_login()

        return self

    def _handle_login(self):
        """Handle login process with both auto and manual options."""
        logger.info("Initiating safe login process...")

        # Navigate to login page
        login_url = f"{self.BASE_URL}/login"
        logger.info(f"Navigating to login page: {login_url}")
        self.page.goto(login_url, wait_until='domcontentloaded')
        self._random_delay(min_delay=2, max_delay=4)

        # Try auto-login if credentials provided
        if self.email and self.password:
            try:
                logger.info("🔐 Attempting auto-login...")
                print("\n🔐 AUTO-LOGIN MODE")
                print("=" * 50)
                print("Attempting to log in automatically...")

                # Fill email
                email_field = self.page.wait_for_selector('#username', timeout=10000)
                email_field.fill(self.email)
                self._random_delay(min_delay=1, max_delay=2)

                # Fill password
                password_field = self.page.query_selector('#password')
                password_field.fill(self.password)
                self._random_delay(min_delay=1, max_delay=2)

                # Click login button
                login_button = self.page.query_selector('button[type="submit"]')
                login_button.click()

                logger.info("Login form submitted, waiting for response...")

            except Exception as e:
                logger.warning(f"Auto-login failed: {e}")
                print("❌ Auto-login failed, falling back to manual login...")

        # Manual login instructions (always shown as fallback)
        if not (self.email and self.password):
            print("\n" + "="*80)
            print("🔐 MANUAL LOGIN REQUIRED FOR ENHANCED DATA ACCESS")
            print("="*80)
            print("A browser window has opened. Please:")
            print("1. Log in to your LinkedIn account manually")
            print("2. Complete any security challenges (2FA, CAPTCHA, etc.)")
            print("3. Wait to be redirected to your LinkedIn feed")
            print("4. DO NOT close the browser window")
            print("\n⚠️  SAFETY MEASURES ACTIVE:")
            print("   - Enhanced delays between actions")
            print("   - Limited search volume")
            print("   - Human-like browsing patterns")
            print("   - Session monitoring")
            print("="*80)

        try:
            # Wait for successful login with multiple indicators
            logger.info("Waiting for login completion...")

            # Try multiple ways to detect successful login
            login_success = False

            # Method 1: Wait for feed URL
            try:
                self.page.wait_for_url(f"{self.BASE_URL}/feed/**", timeout=60000)
                login_success = True
                logger.info("✅ Detected feed URL - login successful")
            except:
                pass

            # Method 2: Check for feed elements
            if not login_success:
                try:
                    self.page.wait_for_selector('[data-test-id="feed-container"], .feed-container, .scaffold-layout__main', timeout=30000)
                    login_success = True
                    logger.info("✅ Detected feed elements - login successful")
                except:
                    pass

            # Method 3: Check if we're no longer on login page
            if not login_success:
                current_url = self.page.url
                if '/login' not in current_url and 'linkedin.com' in current_url:
                    login_success = True
                    logger.info("✅ Navigated away from login page - login successful")

            if login_success:
                self.is_logged_in = True
                logger.info("✅ Login successful! Enhanced data access enabled.")

                # Post-login safety delay
                logger.info("Applying post-login safety delay...")
                self._random_delay(min_delay=5, max_delay=10)

                print("\n✅ Login successful! You can now access enhanced LinkedIn data.")
                print("🛡️  All safety measures are active to protect your account.")
            else:
                raise Exception("Login verification failed")

        except Exception as e:
            logger.error(f"Login failed or timed out: {e}")
            print("\n❌ Login failed or timed out.")
            print("The scraper will continue with limited access.")
            self.is_logged_in = False

    def close(self):
        """Close the browser."""
        if self.browser:
            logger.info("Closing browser")
            self.browser.close()
            self.playwright.stop()
            self.browser = None
            self.context = None
            self.page = None

    def search_posts_by_keywords(self, keywords, hours_back=24):
        """
        Search for LinkedIn posts containing specific keywords with enhanced safety measures.

        Args:
            keywords: List of keywords to search for
            hours_back: Number of hours to look back (default: 24)

        Returns:
            List of dictionaries containing post information
        """
        if not self.page:
            raise RuntimeError("Browser not started")

        # Safety check: limit number of searches
        if len(keywords) > self.MAX_DAILY_SEARCHES:
            logger.warning(f"Limiting keywords to {self.MAX_DAILY_SEARCHES} for account safety")
            keywords = keywords[:self.MAX_DAILY_SEARCHES]

        all_posts = []
        date_threshold = datetime.now() - timedelta(hours=hours_back)

        logger.info(f"🛡️  Safety measures active:")
        logger.info(f"   - Maximum {self.MAX_DAILY_SEARCHES} searches per session")
        logger.info(f"   - {self.SEARCH_DELAY}s delay between searches")
        logger.info(f"   - Maximum {self.max_posts} posts per keyword")
        logger.info(f"   - Enhanced human-like behavior patterns")

        for i, keyword in enumerate(keywords):
            self.search_count += 1
            logger.info(f"🔍 Searching for keyword {i+1}/{len(keywords)}: '{keyword}'")

            try:
                # Pre-search safety delay
                if i > 0:  # Skip delay for first search
                    logger.info(f"⏱️  Safety delay: {self.SEARCH_DELAY}s between searches...")
                    time.sleep(self.SEARCH_DELAY)

                posts = self._search_single_keyword(keyword, date_threshold, hours_back)
                all_posts.extend(posts)
                logger.info(f"✅ Found {len(posts)} posts for keyword '{keyword}'")

                # Additional safety measures for logged-in users
                if self.is_logged_in:
                    # Simulate reading behavior
                    reading_time = random.uniform(10, 25)
                    logger.info(f"📖 Simulating reading time: {reading_time:.1f}s")
                    time.sleep(reading_time)

                    # Occasionally visit profile or other pages to appear more human
                    if random.random() < 0.3:  # 30% chance
                        self._simulate_human_browsing()

            except Exception as e:
                logger.error(f"❌ Error searching for keyword '{keyword}': {e}")
                # Add extra delay after errors
                time.sleep(random.uniform(15, 30))

        # Remove duplicates based on post URL
        unique_posts = []
        seen_urls = set()

        for post in all_posts:
            post_url = post.get('url', '')
            if post_url and post_url not in seen_urls:
                seen_urls.add(post_url)
                unique_posts.append(post)

        logger.info(f"🎯 Total unique posts found: {len(unique_posts)}")
        logger.info(f"📊 Search session stats: {self.search_count} searches performed")

        return unique_posts

    def _simulate_human_browsing(self):
        """Simulate human browsing behavior to avoid detection."""
        try:
            logger.info("🤖 Simulating human browsing behavior...")

            # Randomly choose a human-like action
            actions = [
                self._visit_notifications,
                self._visit_my_network,
                self._scroll_feed,
                self._visit_profile_briefly
            ]

            action = random.choice(actions)
            action()

        except Exception as e:
            logger.debug(f"Error in human browsing simulation: {e}")

    def _visit_notifications(self):
        """Visit notifications page briefly."""
        logger.debug("Visiting notifications...")
        self.page.goto(f"{self.BASE_URL}/notifications/", wait_until='domcontentloaded')
        time.sleep(random.uniform(3, 8))

    def _visit_my_network(self):
        """Visit my network page briefly."""
        logger.debug("Visiting my network...")
        self.page.goto(f"{self.BASE_URL}/mynetwork/", wait_until='domcontentloaded')
        time.sleep(random.uniform(3, 8))

    def _scroll_feed(self):
        """Scroll through the main feed briefly."""
        logger.debug("Scrolling through feed...")
        self.page.goto(f"{self.BASE_URL}/feed/", wait_until='domcontentloaded')
        self._human_like_scroll(max_scrolls=3)

    def _visit_profile_briefly(self):
        """Visit own profile briefly."""
        logger.debug("Visiting own profile...")
        # Try to find profile link
        try:
            profile_link = self.page.query_selector('a[href*="/in/"]')
            if profile_link:
                href = profile_link.get_attribute('href')
                if href:
                    self.page.goto(f"{self.BASE_URL}{href}", wait_until='domcontentloaded')
                    time.sleep(random.uniform(3, 8))
        except Exception:
            # Fallback to feed
            self.page.goto(f"{self.BASE_URL}/feed/", wait_until='domcontentloaded')

    def _search_single_keyword(self, keyword, date_threshold, hours_back):
        """
        Search for posts with a single keyword.

        Args:
            keyword: Keyword to search for
            date_threshold: Date threshold for filtering posts

        Returns:
            List of post dictionaries
        """
        posts = []

        # Construct search URL with correct LinkedIn parameters
        encoded_keyword = quote_plus(keyword)

        # Try multiple URL formats to find one that works
        search_urls = [
            # Original format you provided
            f"{self.SEARCH_URL}?datePosted=%22past-24h%22&keywords={encoded_keyword}&origin=FACETED_SEARCH&sortBy=%22date_posted%22",

            # Alternative format
            f"{self.SEARCH_URL}?keywords={encoded_keyword}&datePosted=past-24h&sortBy=date_posted",

            # Simple format
            f"{self.SEARCH_URL}?keywords={encoded_keyword}",

            # Basic LinkedIn search
            f"https://www.linkedin.com/search/results/all/?keywords={encoded_keyword}"
        ]

        # Try each URL format
        for i, search_url in enumerate(search_urls):
            logger.info(f"Trying search URL format {i+1}: {search_url}")

            try:
                success = self._try_search_url(search_url, keyword, date_threshold)
                if success:
                    return success
            except Exception as e:
                logger.warning(f"Search URL {i+1} failed: {e}")
                continue

        logger.error("All search URL formats failed")
        return []

    def _try_search_url(self, search_url, keyword, date_threshold):
        """Try a specific search URL and return posts if successful."""
        try:
            # Navigate to search page with better loading strategy
            logger.info(f"🌐 Navigating to: {search_url}")

            # If logged in, navigate more carefully
            if self.is_logged_in:
                # First go to feed to ensure we're properly logged in
                logger.info("🔄 Ensuring login state by visiting feed first...")
                self.page.goto(f"{self.BASE_URL}/feed/", wait_until='domcontentloaded', timeout=30000)
                self._random_delay(min_delay=2, max_delay=4)

            # Now navigate to search
            self.page.goto(search_url, wait_until='domcontentloaded', timeout=30000)

            # Wait for search results to load
            logger.info("⏳ Waiting for search results to load...")

            # Give the page more time to load if logged in
            wait_time = 20000 if self.is_logged_in else 15000

            try:
                # Wait for either search results or no results message
                self.page.wait_for_selector(
                    '.search-results-container, .search-no-results, .search-results__list, .reusable-search__result, .search-result',
                    timeout=wait_time
                )
                logger.info("✅ Search results container found")
            except Exception:
                logger.warning("⚠️  Search results container not found, continuing anyway...")
                # Take a screenshot for debugging if logged in
                if self.is_logged_in:
                    try:
                        self.page.screenshot(path=f"debug_search_{keyword}.png")
                        logger.info(f"📸 Debug screenshot saved as debug_search_{keyword}.png")
                    except:
                        pass

            self._random_delay(min_delay=3, max_delay=6)

            # Handle potential login prompts or overlays
            self._handle_overlays()

            # Scroll to load more posts
            self._scroll_and_load_posts()

            # Extract posts from the page
            posts = self._extract_posts_from_page(keyword, date_threshold)

            if posts:
                logger.info(f"✅ Successfully found {len(posts)} posts with this URL format")
                return posts
            else:
                logger.info("❌ No posts found with this URL format")
                return []

        except Exception as e:
            logger.error(f"Error during search: {e}")
            return []



    def _handle_overlays(self):
        """Handle login prompts and other overlays that might appear."""
        try:
            # Wait a moment for any overlays to appear
            time.sleep(2)

            # Try to close any modal dialogs
            modal_selectors = [
                '[data-test-modal-id="sign-up-modal"] button[aria-label="Dismiss"]',
                '.modal__dismiss',
                '.artdeco-modal__dismiss',
                'button[aria-label="Dismiss"]',
                '.sign-up-modal__dismiss-btn'
            ]

            for selector in modal_selectors:
                try:
                    dismiss_btn = self.page.query_selector(selector)
                    if dismiss_btn and dismiss_btn.is_visible():
                        logger.info("Closing modal dialog")
                        dismiss_btn.click()
                        time.sleep(1)
                        break
                except Exception:
                    continue

        except Exception as e:
            logger.debug(f"Error handling overlays: {e}")

    def _scroll_and_load_posts(self, max_scrolls=10):
        """
        Scroll the page to load more posts.

        Args:
            max_scrolls: Maximum number of scroll attempts
        """
        logger.info("Scrolling to load posts...")

        for i in range(max_scrolls):
            # Get current page height
            current_height = self.page.evaluate('() => document.body.scrollHeight')

            # Scroll down
            scroll_amount = random.randint(self.SCROLL_STEP - 100, self.SCROLL_STEP + 100)
            self.page.evaluate(f'window.scrollBy(0, {scroll_amount})')

            # Wait for content to load
            time.sleep(self.SCROLL_DELAY)

            # Check if new content loaded
            new_height = self.page.evaluate('() => document.body.scrollHeight')

            if new_height <= current_height:
                logger.info("No more content to load")
                break

            # Random pause to mimic human behavior
            if random.random() < 0.3:
                time.sleep(random.uniform(1, 3))

        logger.info(f"Completed scrolling after {i + 1} attempts")

    def _extract_posts_from_page(self, keyword, date_threshold):
        """
        Extract posts from the current page.

        Args:
            keyword: The keyword being searched
            date_threshold: Date threshold for filtering posts

        Returns:
            List of post dictionaries
        """
        posts = []

        # Comprehensive selectors for current LinkedIn structure (2024/2025)
        post_selectors = [
            # Primary search result containers
            '.search-results-container .reusable-search__result',
            '.search-results-container .search-result',
            '.search-results-container .search-result__wrapper',
            '.reusable-search__result',
            '.search-result',
            '.search-result__wrapper',

            # Content-specific selectors
            'div[data-chameleon-result-urn]',
            'div[data-urn]',
            'li[data-urn]',
            'div[data-id]',

            # Feed-style selectors (when logged in)
            '.feed-shared-update-v2',
            '.update-components-actor',
            'div[data-id^="urn:li:activity"]',
            '.feed-shared-update',

            # Article and content containers
            'article',
            'article.ember-view',
            '.content-card',
            '.artdeco-card',

            # Generic containers with common patterns
            'div[class*="search-result"]',
            'li[class*="search-result"]',
            'div[class*="reusable-search"]',
            'div[class*="update"]',
            'div[class*="post"]',
            'div[class*="content"]',

            # Fallback - any div with substantial content
            '.ember-view',
            'div'
        ]

        post_elements = []
        for selector in post_selectors:
            elements = self.page.query_selector_all(selector)
            if elements:
                logger.info(f"✅ Found {len(elements)} posts with selector: {selector}")
                post_elements = elements
                break
            else:
                logger.debug(f"❌ No elements found with selector: {selector}")

        if not post_elements:
            logger.warning("❌ No post elements found on page")
            # Let's debug what's actually on the page
            logger.info("🔍 Debugging: Checking page content...")
            try:
                page_text = self.page.inner_text('body')
                if 'sign in' in page_text.lower() or 'log in' in page_text.lower():
                    logger.warning("⚠️  Page appears to require login")
                elif 'no results' in page_text.lower():
                    logger.info("ℹ️  LinkedIn reports no results for this search")
                else:
                    logger.info(f"📄 Page contains {len(page_text)} characters of content")
            except Exception as e:
                logger.debug(f"Debug error: {e}")
            return posts

        logger.info(f"Processing {len(post_elements)} post elements...")

        for i, post_element in enumerate(post_elements):
            if len(posts) >= self.max_posts:
                logger.info(f"Reached maximum posts limit ({self.max_posts})")
                break

            try:
                post_data = self._extract_single_post(post_element, keyword, date_threshold)
                if post_data:
                    posts.append(post_data)
                    logger.debug(f"Extracted post {i + 1}: {post_data.get('content', '')[:100]}...")

            except Exception as e:
                logger.error(f"Error extracting post {i + 1}: {e}")

        return posts

    def _extract_single_post(self, post_element, keyword, date_threshold):
        """
        Extract data from a single post element.

        Args:
            post_element: Post element from the page
            keyword: The keyword being searched
            date_threshold: Date threshold for filtering

        Returns:
            Post dictionary or None if extraction fails
        """
        try:
            # Extract post content
            content = self._extract_post_content(post_element)
            if not content or keyword.lower() not in content.lower():
                return None

            # Extract date
            post_date = self._extract_post_date(post_element)
            if not post_date or post_date < date_threshold:
                return None

            # Extract other metadata
            author_name = self._extract_author_name(post_element)
            author_title = self._extract_author_title(post_element)
            post_url = self._extract_post_url(post_element)

            # Skip if we've already collected this post
            if post_url and post_url in self.collected_posts:
                return None

            if post_url:
                self.collected_posts.add(post_url)

            return {
                'keyword': keyword,
                'content': content,
                'author_name': author_name,
                'author_title': author_title,
                'date': post_date.isoformat() if post_date else None,
                'url': post_url,
                'extracted_at': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error extracting single post: {e}")
            return None

    def _extract_post_content(self, post_element):
        """Extract the main content text from a post element."""
        # Updated content selectors for current LinkedIn structure
        content_selectors = [
            # Search result content selectors (most important for search pages)
            '.search-result__primary-info',
            '.search-result__content',
            '.search-result__summary',
            '.reusable-search__result-text',
            '.search-result__wrapper .text-body-medium',
            '.search-result__wrapper .text-body-small',

            # Feed content selectors
            '.feed-shared-text__text-view',
            '.update-components-text',
            '.feed-shared-update-v2__description-wrapper',
            '.feed-shared-text',
            '.feed-shared-inline-show-more-text',
            'div[data-test-id="main-feed-activity-card__commentary"]',
            '.feed-shared-update-v2__content',
            '.feed-shared-article__description',

            # Generic content selectors
            '.content-card__text',
            '.content-card__description',
            'span.break-words',
            '.text-body-medium',
            '.text-body-small',

            # Fallback selectors
            'div[class*="text"]',
            'span[class*="text"]',
            'p'
        ]

        for selector in content_selectors:
            try:
                element = post_element.query_selector(selector)
                if element:
                    text = element.inner_text().strip()
                    if text and len(text) > 10:  # Ensure we have meaningful content
                        return text
            except Exception:
                continue

        # Fallback: get all text from the post element
        try:
            text = post_element.inner_text().strip()
            # Clean up common UI elements
            text = re.sub(r'Like|Comment|Share|Send|Copy|Report|Save', '', text)
            text = re.sub(r'\s+', ' ', text).strip()
            return text[:2000] if len(text) > 2000 else text
        except Exception:
            return ""

    def _extract_post_date(self, post_element):
        """Extract the post date from a post element."""
        date_selectors = [
            '.update-components-actor__sub-description',
            '.feed-shared-actor__sub-description',
            '.feed-shared-actor__sub-description span',
            'time',
            '.artdeco-entity-lockup__caption',
            'span.visually-hidden'
        ]

        for selector in date_selectors:
            try:
                element = post_element.query_selector(selector)
                if element:
                    date_text = element.inner_text().strip()
                    if date_text:
                        return self._parse_linkedin_date(date_text)
            except Exception:
                continue

        return None

    def _extract_author_name(self, post_element) -> str:
        """Extract the author name from a post element."""
        name_selectors = [
            '.update-components-actor__name',
            '.feed-shared-actor__name',
            '.feed-shared-actor__title',
            '.update-components-actor__title',
            'a[data-control-name="actor"] span[aria-hidden="true"]'
        ]

        for selector in name_selectors:
            try:
                element = post_element.query_selector(selector)
                if element:
                    name = element.inner_text().strip()
                    if name:
                        return name
            except Exception:
                continue

        return ""

    def _extract_author_title(self, post_element) -> str:
        """Extract the author title/headline from a post element."""
        title_selectors = [
            '.update-components-actor__description',
            '.feed-shared-actor__description',
            '.feed-shared-actor__sub-description',
            '.update-components-actor__sub-description'
        ]

        for selector in title_selectors:
            try:
                element = post_element.query_selector(selector)
                if element:
                    title = element.inner_text().strip()
                    if title and not any(time_word in title.lower() for time_word in ['ago', 'hour', 'day', 'week', 'month']):
                        return title
            except Exception:
                continue

        return ""

    def _extract_post_url(self, post_element):
        """Extract the post URL from a post element."""
        url_selectors = [
            'a[href*="/feed/update/"]',
            'a[href*="/posts/"]',
            'a.app-aware-link[href*="linkedin.com"]',
            '.feed-shared-update-v2__permalink',
            '.update-components-actor__sub-description-link'
        ]

        for selector in url_selectors:
            try:
                element = post_element.query_selector(selector)
                if element:
                    href = element.get_attribute('href')
                    if href:
                        if href.startswith('/'):
                            return f"{self.BASE_URL}{href}"
                        elif 'linkedin.com' in href:
                            return href
            except Exception:
                continue

        return None

    def _parse_linkedin_date(self, date_str):
        """
        Parse LinkedIn date format.

        Args:
            date_str: Date string from LinkedIn

        Returns:
            Datetime object or None
        """
        try:
            # Clean up the date string
            date_str = date_str.lower().strip()

            # Remove any prefix like "Posted: " or "Shared: "
            if ':' in date_str and not date_str[0].isdigit():
                date_str = date_str.split(':', 1)[1].strip()

            now = datetime.now()

            # Handle relative time formats
            if any(term in date_str for term in ['now', 'just now', 'few seconds', 'moments']):
                return now

            # Extract number and unit from relative time strings
            time_patterns = [
                (r'(\d+)\s*(?:minute|min)s?', 'minutes'),
                (r'(\d+)\s*(?:hour|hr)s?', 'hours'),
                (r'(\d+)\s*(?:day)s?', 'days'),
                (r'(\d+)\s*(?:week|wk)s?', 'weeks'),
                (r'(\d+)\s*(?:month|mo)s?', 'months'),
                (r'(\d+)\s*(?:year|yr)s?', 'years')
            ]

            for pattern, unit in time_patterns:
                match = re.search(pattern, date_str)
                if match:
                    value = int(match.group(1))

                    if unit == 'minutes':
                        return now - timedelta(minutes=value)
                    elif unit == 'hours':
                        return now - timedelta(hours=value)
                    elif unit == 'days':
                        return now - timedelta(days=value)
                    elif unit == 'weeks':
                        return now - timedelta(weeks=value)
                    elif unit == 'months':
                        return now - timedelta(days=value * 30)
                    elif unit == 'years':
                        return now - timedelta(days=value * 365)

            # Handle "yesterday" and "today"
            if 'yesterday' in date_str:
                return now - timedelta(days=1)
            if 'today' in date_str:
                return now

            # As a fallback, assume it's recent (within the last week)
            return now - timedelta(days=7)

        except Exception as e:
            logger.error(f"Error parsing date '{date_str}': {e}")
            return None

    def _random_delay(self, min_delay=None, max_delay=None):
        """
        Add a random delay between actions to mimic human behavior.

        Args:
            min_delay: Minimum delay in seconds
            max_delay: Maximum delay in seconds
        """
        min_delay = min_delay if min_delay is not None else self.MIN_DELAY
        max_delay = max_delay if max_delay is not None else self.MAX_DELAY

        delay = random.uniform(min_delay, max_delay)
        logger.debug(f"Random delay: {delay:.2f} seconds")
        time.sleep(delay)

    def _add_stealth_scripts(self):
        """Add stealth scripts to avoid detection."""
        if not self.context:
            return

        # Override specific JavaScript properties that automation detection scripts check
        self.context.add_init_script("""
        () => {
            // Override navigator properties
            Object.defineProperty(navigator, 'webdriver', {
                get: () => false
            });

            // Override permissions
            Object.defineProperty(navigator, 'permissions', {
                get: () => {
                    return {
                        query: async () => {
                            return { state: 'prompt' };
                        }
                    };
                }
            });

            // Add random screen resolution
            Object.defineProperty(window, 'screen', {
                get: () => {
                    return {
                        width: Math.floor(Math.random() * 100) + 1920,
                        height: Math.floor(Math.random() * 100) + 1080,
                        availWidth: Math.floor(Math.random() * 100) + 1920,
                        availHeight: Math.floor(Math.random() * 100) + 1080,
                        colorDepth: 24,
                        pixelDepth: 24
                    };
                }
            });

            // Override chrome runtime
            if (window.chrome) {
                Object.defineProperty(window.chrome, 'runtime', {
                    get: () => undefined
                });
            }
        }
        """)
