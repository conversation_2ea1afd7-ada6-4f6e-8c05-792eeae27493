#!/usr/bin/env python3
"""
LinkedIn Keyword Post Scraper - No-login scraper for LinkedIn posts by keywords
"""

import logging
import random
import re
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set
from urllib.parse import quote_plus, urljoin
import json

from playwright.sync_api import sync_playwright, <PERSON>, <PERSON>rows<PERSON>, BrowserContext

logger = logging.getLogger(__name__)

class LinkedInKeywordScraper:
    """
    A no-login LinkedIn scraper that searches for posts containing specific keywords
    within the last 24 hours, implementing safety measures to avoid detection.
    """

    # LinkedIn URLs
    BASE_URL = "https://www.linkedin.com"
    SEARCH_URL = f"{BASE_URL}/search/results/content/"

    # Safety parameters
    MIN_DELAY = 3  # Minimum delay between actions in seconds
    MAX_DELAY = 8  # Maximum delay between actions in seconds
    SCROLL_DELAY = 1.0  # Delay during scrolling in seconds
    SCROLL_STEP = 400  # Pixels to scroll each step
    MAX_POSTS_PER_KEYWORD = 50  # Maximum posts to collect per keyword

    def __init__(self, headless: bool = True, max_posts: int = 50):
        """
        Initialize the LinkedIn keyword scraper.

        Args:
            headless: Whether to run the browser in headless mode
            max_posts: Maximum number of posts to collect per keyword
        """
        self.headless = headless
        self.max_posts = max_posts
        self.playwright = None
        self.browser = None
        self.context = None
        self.page = None
        self.collected_posts = set()  # Track collected post URLs to avoid duplicates

    def __enter__(self):
        """Context manager entry point."""
        self.start()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit point."""
        self.close()

    def start(self):
        """Start the browser."""
        logger.info("Starting browser for no-login LinkedIn scraping")

        # Initialize Playwright
        self.playwright = sync_playwright().start()

        # Browser arguments to avoid detection
        browser_args = [
            '--disable-blink-features=AutomationControlled',
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-infobars',
            '--window-size=1366,768',
            '--disable-extensions',
            '--disable-dev-shm-usage',
            '--disable-gpu',
            '--no-first-run',
            '--disable-default-apps',
            '--disable-features=VizDisplayCompositor'
        ]

        # Launch browser
        try:
            logger.info("Launching Chrome...")
            self.browser = self.playwright.chromium.launch(
                headless=self.headless,
                args=browser_args,
                channel="chrome"
            )
            logger.info("Chrome launched successfully")
        except Exception as e:
            logger.warning(f"Failed to launch Chrome: {e}")
            logger.info("Launching Chromium...")
            self.browser = self.playwright.chromium.launch(
                headless=self.headless,
                args=browser_args
            )
            logger.info("Chromium launched successfully")

        # Create context with realistic user agent and settings
        self.context = self.browser.new_context(
            viewport={'width': 1366, 'height': 768},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            locale='en-US',
            timezone_id='America/New_York',
            has_touch=False,
            is_mobile=False,
            java_script_enabled=True,
            accept_downloads=False,
            ignore_https_errors=True
        )

        # Add stealth scripts
        self._add_stealth_scripts()

        # Create page
        self.page = self.context.new_page()

        # Set additional headers
        self.page.set_extra_http_headers({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })

        return self

    def close(self):
        """Close the browser."""
        if self.browser:
            logger.info("Closing browser")
            self.browser.close()
            self.playwright.stop()
            self.browser = None
            self.context = None
            self.page = None

    def search_posts_by_keywords(self, keywords: List[str], hours_back: int = 24) -> List[Dict]:
        """
        Search for LinkedIn posts containing specific keywords within the specified time frame.

        Args:
            keywords: List of keywords to search for
            hours_back: Number of hours to look back (default: 24)

        Returns:
            List of dictionaries containing post information
        """
        if not self.page:
            raise RuntimeError("Browser not started")

        all_posts = []
        date_threshold = datetime.now() - timedelta(hours=hours_back)

        for keyword in keywords:
            logger.info(f"Searching for posts with keyword: '{keyword}'")

            try:
                posts = self._search_single_keyword(keyword, date_threshold)
                all_posts.extend(posts)
                logger.info(f"Found {len(posts)} posts for keyword '{keyword}'")

                # Random delay between keyword searches
                self._random_delay(min_delay=5, max_delay=10)

            except Exception as e:
                logger.error(f"Error searching for keyword '{keyword}': {e}")

        # Remove duplicates based on post URL
        unique_posts = []
        seen_urls = set()

        for post in all_posts:
            post_url = post.get('url', '')
            if post_url and post_url not in seen_urls:
                seen_urls.add(post_url)
                unique_posts.append(post)

        logger.info(f"Total unique posts found: {len(unique_posts)}")
        return unique_posts

    def _search_single_keyword(self, keyword: str, date_threshold: datetime) -> List[Dict]:
        """
        Search for posts with a single keyword.

        Args:
            keyword: Keyword to search for
            date_threshold: Date threshold for filtering posts

        Returns:
            List of post dictionaries
        """
        posts = []

        # Construct search URL
        encoded_keyword = quote_plus(keyword)
        search_url = f"{self.SEARCH_URL}?keywords={encoded_keyword}&sortBy=date_posted"

        logger.info(f"Navigating to search URL: {search_url}")

        try:
            # Navigate to search page
            self.page.goto(search_url, wait_until='networkidle', timeout=30000)
            self._random_delay()

            # Handle potential login prompts or overlays
            self._handle_overlays()

            # Scroll to load more posts
            self._scroll_and_load_posts()

            # Extract posts from the page
            posts = self._extract_posts_from_page(keyword, date_threshold)

        except Exception as e:
            logger.error(f"Error during search for keyword '{keyword}': {e}")

        return posts

    def _handle_overlays(self):
        """Handle login prompts and other overlays that might appear."""
        try:
            # Wait a moment for any overlays to appear
            time.sleep(2)

            # Try to close any modal dialogs
            modal_selectors = [
                '[data-test-modal-id="sign-up-modal"] button[aria-label="Dismiss"]',
                '.modal__dismiss',
                '.artdeco-modal__dismiss',
                'button[aria-label="Dismiss"]',
                '.sign-up-modal__dismiss-btn'
            ]

            for selector in modal_selectors:
                try:
                    dismiss_btn = self.page.query_selector(selector)
                    if dismiss_btn and dismiss_btn.is_visible():
                        logger.info("Closing modal dialog")
                        dismiss_btn.click()
                        time.sleep(1)
                        break
                except Exception:
                    continue

        except Exception as e:
            logger.debug(f"Error handling overlays: {e}")

    def _scroll_and_load_posts(self, max_scrolls: int = 10):
        """
        Scroll the page to load more posts.

        Args:
            max_scrolls: Maximum number of scroll attempts
        """
        logger.info("Scrolling to load posts...")

        for i in range(max_scrolls):
            # Get current page height
            current_height = self.page.evaluate('() => document.body.scrollHeight')

            # Scroll down
            scroll_amount = random.randint(self.SCROLL_STEP - 100, self.SCROLL_STEP + 100)
            self.page.evaluate(f'window.scrollBy(0, {scroll_amount})')

            # Wait for content to load
            time.sleep(self.SCROLL_DELAY)

            # Check if new content loaded
            new_height = self.page.evaluate('() => document.body.scrollHeight')

            if new_height <= current_height:
                logger.info("No more content to load")
                break

            # Random pause to mimic human behavior
            if random.random() < 0.3:
                time.sleep(random.uniform(1, 3))

        logger.info(f"Completed scrolling after {i + 1} attempts")

    def _extract_posts_from_page(self, keyword: str, date_threshold: datetime) -> List[Dict]:
        """
        Extract posts from the current page.

        Args:
            keyword: The keyword being searched
            date_threshold: Date threshold for filtering posts

        Returns:
            List of post dictionaries
        """
        posts = []

        # Multiple selectors for post containers (LinkedIn changes these frequently)
        post_selectors = [
            '.feed-shared-update-v2',
            '.update-components-actor',
            'div[data-id^="urn:li:activity"]',
            '.artdeco-card',
            '.feed-shared-update',
            '.search-result__wrapper',
            '.search-result',
            '.content-card',
            'article',
            '.ember-view'
        ]

        post_elements = []
        for selector in post_selectors:
            elements = self.page.query_selector_all(selector)
            if elements:
                logger.info(f"Found {len(elements)} posts with selector: {selector}")
                post_elements = elements
                break

        if not post_elements:
            logger.warning("No post elements found on page")
            return posts

        logger.info(f"Processing {len(post_elements)} post elements...")

        for i, post_element in enumerate(post_elements):
            if len(posts) >= self.max_posts:
                logger.info(f"Reached maximum posts limit ({self.max_posts})")
                break

            try:
                post_data = self._extract_single_post(post_element, keyword, date_threshold)
                if post_data:
                    posts.append(post_data)
                    logger.debug(f"Extracted post {i + 1}: {post_data.get('content', '')[:100]}...")

            except Exception as e:
                logger.error(f"Error extracting post {i + 1}: {e}")

        return posts

    def _extract_single_post(self, post_element, keyword: str, date_threshold: datetime) -> Optional[Dict]:
        """
        Extract data from a single post element.

        Args:
            post_element: Post element from the page
            keyword: The keyword being searched
            date_threshold: Date threshold for filtering

        Returns:
            Post dictionary or None if extraction fails
        """
        try:
            # Extract post content
            content = self._extract_post_content(post_element)
            if not content or keyword.lower() not in content.lower():
                return None

            # Extract date
            post_date = self._extract_post_date(post_element)
            if not post_date or post_date < date_threshold:
                return None

            # Extract other metadata
            author_name = self._extract_author_name(post_element)
            author_title = self._extract_author_title(post_element)
            post_url = self._extract_post_url(post_element)

            # Skip if we've already collected this post
            if post_url and post_url in self.collected_posts:
                return None

            if post_url:
                self.collected_posts.add(post_url)

            return {
                'keyword': keyword,
                'content': content,
                'author_name': author_name,
                'author_title': author_title,
                'date': post_date.isoformat() if post_date else None,
                'url': post_url,
                'extracted_at': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error extracting single post: {e}")
            return None

    def _extract_post_content(self, post_element) -> str:
        """Extract the main content text from a post element."""
        content_selectors = [
            '.feed-shared-text__text-view',
            '.update-components-text',
            '.feed-shared-update-v2__description-wrapper',
            '.feed-shared-text',
            '.feed-shared-inline-show-more-text',
            'div[data-test-id="main-feed-activity-card__commentary"]',
            '.feed-shared-update-v2__content',
            '.feed-shared-article__description',
            'span.break-words'
        ]

        for selector in content_selectors:
            try:
                element = post_element.query_selector(selector)
                if element:
                    text = element.inner_text().strip()
                    if text and len(text) > 10:  # Ensure we have meaningful content
                        return text
            except Exception:
                continue

        # Fallback: get all text from the post element
        try:
            text = post_element.inner_text().strip()
            # Clean up common UI elements
            text = re.sub(r'Like|Comment|Share|Send|Copy|Report|Save', '', text)
            text = re.sub(r'\s+', ' ', text).strip()
            return text[:2000] if len(text) > 2000 else text
        except Exception:
            return ""

    def _extract_post_date(self, post_element) -> Optional[datetime]:
        """Extract the post date from a post element."""
        date_selectors = [
            '.update-components-actor__sub-description',
            '.feed-shared-actor__sub-description',
            '.feed-shared-actor__sub-description span',
            'time',
            '.artdeco-entity-lockup__caption',
            'span.visually-hidden'
        ]

        for selector in date_selectors:
            try:
                element = post_element.query_selector(selector)
                if element:
                    date_text = element.inner_text().strip()
                    if date_text:
                        return self._parse_linkedin_date(date_text)
            except Exception:
                continue

        return None

    def _extract_author_name(self, post_element) -> str:
        """Extract the author name from a post element."""
        name_selectors = [
            '.update-components-actor__name',
            '.feed-shared-actor__name',
            '.feed-shared-actor__title',
            '.update-components-actor__title',
            'a[data-control-name="actor"] span[aria-hidden="true"]'
        ]

        for selector in name_selectors:
            try:
                element = post_element.query_selector(selector)
                if element:
                    name = element.inner_text().strip()
                    if name:
                        return name
            except Exception:
                continue

        return ""

    def _extract_author_title(self, post_element) -> str:
        """Extract the author title/headline from a post element."""
        title_selectors = [
            '.update-components-actor__description',
            '.feed-shared-actor__description',
            '.feed-shared-actor__sub-description',
            '.update-components-actor__sub-description'
        ]

        for selector in title_selectors:
            try:
                element = post_element.query_selector(selector)
                if element:
                    title = element.inner_text().strip()
                    if title and not any(time_word in title.lower() for time_word in ['ago', 'hour', 'day', 'week', 'month']):
                        return title
            except Exception:
                continue

        return ""

    def _extract_post_url(self, post_element) -> Optional[str]:
        """Extract the post URL from a post element."""
        url_selectors = [
            'a[href*="/feed/update/"]',
            'a[href*="/posts/"]',
            'a.app-aware-link[href*="linkedin.com"]',
            '.feed-shared-update-v2__permalink',
            '.update-components-actor__sub-description-link'
        ]

        for selector in url_selectors:
            try:
                element = post_element.query_selector(selector)
                if element:
                    href = element.get_attribute('href')
                    if href:
                        if href.startswith('/'):
                            return f"{self.BASE_URL}{href}"
                        elif 'linkedin.com' in href:
                            return href
            except Exception:
                continue

        return None

    def _parse_linkedin_date(self, date_str: str) -> Optional[datetime]:
        """
        Parse LinkedIn date format.

        Args:
            date_str: Date string from LinkedIn

        Returns:
            Datetime object or None
        """
        try:
            # Clean up the date string
            date_str = date_str.lower().strip()

            # Remove any prefix like "Posted: " or "Shared: "
            if ':' in date_str and not date_str[0].isdigit():
                date_str = date_str.split(':', 1)[1].strip()

            now = datetime.now()

            # Handle relative time formats
            if any(term in date_str for term in ['now', 'just now', 'few seconds', 'moments']):
                return now

            # Extract number and unit from relative time strings
            time_patterns = [
                (r'(\d+)\s*(?:minute|min)s?', 'minutes'),
                (r'(\d+)\s*(?:hour|hr)s?', 'hours'),
                (r'(\d+)\s*(?:day)s?', 'days'),
                (r'(\d+)\s*(?:week|wk)s?', 'weeks'),
                (r'(\d+)\s*(?:month|mo)s?', 'months'),
                (r'(\d+)\s*(?:year|yr)s?', 'years')
            ]

            for pattern, unit in time_patterns:
                match = re.search(pattern, date_str)
                if match:
                    value = int(match.group(1))

                    if unit == 'minutes':
                        return now - timedelta(minutes=value)
                    elif unit == 'hours':
                        return now - timedelta(hours=value)
                    elif unit == 'days':
                        return now - timedelta(days=value)
                    elif unit == 'weeks':
                        return now - timedelta(weeks=value)
                    elif unit == 'months':
                        return now - timedelta(days=value * 30)
                    elif unit == 'years':
                        return now - timedelta(days=value * 365)

            # Handle "yesterday" and "today"
            if 'yesterday' in date_str:
                return now - timedelta(days=1)
            if 'today' in date_str:
                return now

            # As a fallback, assume it's recent (within the last week)
            return now - timedelta(days=7)

        except Exception as e:
            logger.error(f"Error parsing date '{date_str}': {e}")
            return None

    def _random_delay(self, min_delay: float = None, max_delay: float = None):
        """
        Add a random delay between actions to mimic human behavior.

        Args:
            min_delay: Minimum delay in seconds
            max_delay: Maximum delay in seconds
        """
        min_delay = min_delay if min_delay is not None else self.MIN_DELAY
        max_delay = max_delay if max_delay is not None else self.MAX_DELAY

        delay = random.uniform(min_delay, max_delay)
        logger.debug(f"Random delay: {delay:.2f} seconds")
        time.sleep(delay)

    def _add_stealth_scripts(self):
        """Add stealth scripts to avoid detection."""
        if not self.context:
            return

        # Override specific JavaScript properties that automation detection scripts check
        self.context.add_init_script("""
        () => {
            // Override navigator properties
            Object.defineProperty(navigator, 'webdriver', {
                get: () => false
            });

            // Override permissions
            Object.defineProperty(navigator, 'permissions', {
                get: () => {
                    return {
                        query: async () => {
                            return { state: 'prompt' };
                        }
                    };
                }
            });

            // Add random screen resolution
            Object.defineProperty(window, 'screen', {
                get: () => {
                    return {
                        width: Math.floor(Math.random() * 100) + 1920,
                        height: Math.floor(Math.random() * 100) + 1080,
                        availWidth: Math.floor(Math.random() * 100) + 1920,
                        availHeight: Math.floor(Math.random() * 100) + 1080,
                        colorDepth: 24,
                        pixelDepth: 24
                    };
                }
            });

            // Override chrome runtime
            if (window.chrome) {
                Object.defineProperty(window.chrome, 'runtime', {
                    get: () => undefined
                });
            }
        }
        """)
