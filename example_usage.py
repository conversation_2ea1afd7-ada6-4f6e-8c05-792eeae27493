#!/usr/bin/env python3
"""
Example usage script for LinkedIn Keyword Scraper with login
Demonstrates safe usage patterns to protect your LinkedIn account
"""

import logging
from linkedin_keyword_scraper import LinkedInKeywordScraper

def safe_linkedin_search_example():
    """
    Example of safe LinkedIn keyword searching with login.
    This demonstrates best practices for account protection.
    """

    # Configure logging
    logging.basicConfig(level=logging.INFO)

    # Define keywords (keep it small for safety)
    keywords = [
        "artificial intelligence",
        "machine learning",
        "python developer"
    ]

    print("🔐 LinkedIn Safe Keyword Search Example")
    print("=" * 50)
    print("This example demonstrates:")
    print("✅ Manual login for enhanced data access")
    print("🛡️  Maximum safety measures to protect your account")
    print("⏱️  Human-like delays and behavior patterns")
    print("📊 Limited search volume for account safety")
    print("=" * 50)

    try:
        # Initialize scraper with safety settings
        with LinkedInKeywordScraper(
            headless=False,          # Visible browser for login
            max_posts=15,            # Conservative limit
            require_login=True       # Enable login for better data
        ) as scraper:

            print(f"\n🔍 Searching for {len(keywords)} keywords...")
            print("Keywords:", ", ".join(keywords))

            # Perform the search
            posts = scraper.search_posts_by_keywords(
                keywords=keywords,
                hours_back=24  # Last 24 hours
            )

            # Display results
            print(f"\n📊 Results Summary:")
            print(f"Total posts found: {len(posts)}")

            if posts:
                print(f"\n📝 Sample posts:")
                for i, post in enumerate(posts[:3]):  # Show first 3
                    print(f"\n{i+1}. Author: {post.get('author_name', 'Unknown')}")
                    print(f"   Date: {post.get('date', 'Unknown')}")
                    print(f"   Keyword: {post.get('keyword', 'Unknown')}")
                    content = post.get('content', '')
                    preview = content[:100] + "..." if len(content) > 100 else content
                    print(f"   Content: {preview}")

                # Save results
                import csv
                with open('example_results.csv', 'w', newline='', encoding='utf-8') as f:
                    fieldnames = ['keyword', 'author_name', 'author_title', 'date', 'content', 'url']
                    writer = csv.DictWriter(f, fieldnames=fieldnames)
                    writer.writeheader()
                    for post in posts:
                        writer.writerow({field: post.get(field, '') for field in fieldnames})

                print(f"\n💾 Results saved to 'example_results.csv'")
            else:
                print("\n⚠️  No posts found. This could be due to:")
                print("   - Very recent keywords with no new posts")
                print("   - LinkedIn's content restrictions")
                print("   - Network connectivity issues")

            print(f"\n✅ Search completed safely!")
            print(f"🛡️  Your account protection measures were active throughout.")

    except KeyboardInterrupt:
        print("\n⏹️  Search interrupted by user")
    except Exception as e:
        print(f"\n❌ Error during search: {e}")
        print("💡 Try running with --debug flag for more information")

def no_login_example():
    """
    Example of no-login mode (limited results).
    """
    print("\n🌐 No-Login Mode Example")
    print("=" * 30)
    print("⚠️  This mode has very limited access to LinkedIn content")

    try:
        with LinkedInKeywordScraper(
            headless=False,
            max_posts=10,
            require_login=False  # No login mode
        ) as scraper:

            posts = scraper.search_posts_by_keywords(
                keywords=["python"],
                hours_back=24
            )

            print(f"Posts found in no-login mode: {len(posts)}")

    except Exception as e:
        print(f"No-login search error: {e}")

if __name__ == "__main__":
    print("LinkedIn Keyword Scraper - Safe Usage Examples")
    print("=" * 60)

    choice = input("\nChoose mode:\n1. Login mode (recommended)\n2. No-login mode\n\nEnter choice (1 or 2): ").strip()

    if choice == "1":
        safe_linkedin_search_example()
    elif choice == "2":
        no_login_example()
    else:
        print("Invalid choice. Running login mode example...")
        safe_linkedin_search_example()
