#!/usr/bin/env python3
"""
LinkedIn Keyword Search CLI - Main script for searching LinkedIn posts by keywords
"""

import argparse
import csv
import json
import logging
import sys
import getpass
from datetime import datetime
from pathlib import Path

from linkedin_keyword_scraper import LinkedIn<PERSON>eywordScraper

def setup_logging(debug=False):
    """Configure logging with appropriate level."""
    level = logging.DEBUG if debug else logging.INFO

    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler("linkedin_keyword_search.log"),
            logging.StreamHandler()
        ]
    )

    logger = logging.getLogger(__name__)
    logger.setLevel(level)

    if debug:
        logger.debug("Debug logging enabled")

    return logger

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description='LinkedIn Keyword Post Scraper - Search for posts containing specific keywords',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python keyword_search.py --keywords "artificial intelligence" "machine learning" --hours 24 --output results.csv
  python keyword_search.py -k "python developer" -k "remote work" --hours 48 --format json --output results.json
  python keyword_search.py --keywords-file keywords.txt --max-posts 100 --visible
        """
    )

    # Keyword input options
    keyword_group = parser.add_mutually_exclusive_group(required=True)
    keyword_group.add_argument(
        '--keywords', '-k',
        nargs='+',
        help='Keywords to search for (space-separated)'
    )
    keyword_group.add_argument(
        '--keywords-file', '-f',
        type=str,
        help='File containing keywords (one per line)'
    )

    # Time filtering
    parser.add_argument(
        '--hours',
        type=int,
        default=24,
        help='Number of hours to look back for posts (default: 24)'
    )

    # Output options
    parser.add_argument(
        '--output', '-o',
        type=str,
        default='linkedin_keyword_results.csv',
        help='Output file path (default: linkedin_keyword_results.csv)'
    )

    parser.add_argument(
        '--format',
        choices=['csv', 'json'],
        default='csv',
        help='Output format (default: csv)'
    )

    # Scraping options
    parser.add_argument(
        '--max-posts',
        type=int,
        default=25,
        help='Maximum number of posts to collect per keyword (default: 25, reduced for safety)'
    )

    parser.add_argument(
        '--no-login',
        action='store_true',
        help='Skip login and use limited public access (not recommended)'
    )

    parser.add_argument(
        '--headless',
        action='store_true',
        help='Run browser in headless mode (not recommended for login)'
    )

    # Auto-login options
    parser.add_argument(
        '--email',
        type=str,
        help='LinkedIn email for auto-login (optional)'
    )

    parser.add_argument(
        '--password',
        type=str,
        help='LinkedIn password for auto-login (optional)'
    )

    # Logging
    parser.add_argument(
        '--debug',
        action='store_true',
        help='Enable debug logging'
    )

    return parser.parse_args()

def read_keywords_from_file(file_path):
    """Read keywords from a file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            keywords = [line.strip() for line in f if line.strip()]
        return keywords
    except Exception as e:
        logger.error(f"Error reading keywords file: {e}")
        sys.exit(1)

def save_results_csv(posts, output_path):
    """Save results to CSV file."""
    if not posts:
        logger.warning("No posts to save")
        return

    fieldnames = ['keyword', 'author_name', 'author_title', 'date', 'content', 'url', 'extracted_at']

    try:
        with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for post in posts:
                # Ensure all required fields are present
                row = {field: post.get(field, '') for field in fieldnames}
                writer.writerow(row)

        logger.info(f"Results saved to {output_path}")

    except Exception as e:
        logger.error(f"Error saving CSV file: {e}")

def save_results_json(posts, output_path):
    """Save results to JSON file."""
    if not posts:
        logger.warning("No posts to save")
        return

    try:
        with open(output_path, 'w', encoding='utf-8') as jsonfile:
            json.dump(posts, jsonfile, indent=2, ensure_ascii=False)

        logger.info(f"Results saved to {output_path}")

    except Exception as e:
        logger.error(f"Error saving JSON file: {e}")

def print_summary(posts, keywords, hours):
    """Print a summary of the results."""
    print(f"\n{'='*60}")
    print("LINKEDIN KEYWORD SEARCH SUMMARY")
    print(f"{'='*60}")
    print(f"Keywords searched: {', '.join(keywords)}")
    print(f"Time range: Last {hours} hours")
    print(f"Total posts found: {len(posts)}")

    if posts:
        # Group by keyword
        keyword_counts = {}
        for post in posts:
            keyword = post.get('keyword', 'Unknown')
            keyword_counts[keyword] = keyword_counts.get(keyword, 0) + 1

        print(f"\nPosts per keyword:")
        for keyword, count in keyword_counts.items():
            print(f"  - '{keyword}': {count} posts")

        # Show recent posts
        print(f"\nMost recent posts:")
        sorted_posts = sorted(posts, key=lambda x: x.get('date', ''), reverse=True)
        for i, post in enumerate(sorted_posts[:5]):
            content_preview = post.get('content', '')[:100] + "..." if len(post.get('content', '')) > 100 else post.get('content', '')
            print(f"  {i+1}. {post.get('author_name', 'Unknown')} - {content_preview}")

    print(f"{'='*60}\n")

# Initialize logger
logger = setup_logging()

def main():
    """Main function."""
    args = parse_arguments()

    # Set up logging with debug level if requested
    global logger
    logger = setup_logging(args.debug)

    # Get keywords
    if args.keywords:
        keywords = args.keywords
    else:
        keywords = read_keywords_from_file(args.keywords_file)

    if not keywords:
        logger.error("No keywords provided")
        sys.exit(1)

    logger.info(f"Searching for keywords: {', '.join(keywords)}")
    logger.info(f"Looking back {args.hours} hours")
    logger.info(f"Maximum posts per keyword: {args.max_posts}")

    # Handle password input
    password = args.password
    if args.email and not password:
        password = getpass.getpass("Enter your LinkedIn password: ")

    # Initialize scraper with enhanced safety settings
    try:
        require_login = not args.no_login

        if require_login:
            print("\n🔐 LOGIN MODE ENABLED")
            print("=" * 50)
            print("✅ Enhanced data access with full LinkedIn content")
            print("🛡️  Maximum safety measures active to protect your account")
            print("⏱️  Slower operation with human-like delays")
            print("📊 Limited to 25 posts per keyword for safety")
            print("=" * 50)
            print("\n⚠️  IMPORTANT: When the browser opens:")
            print("1. Complete the LinkedIn login process")
            print("2. Wait to be redirected to your feed")
            print("3. The scraper will automatically continue")
            print("=" * 50)
        else:
            print("\n🌐 NO-LOGIN MODE")
            print("=" * 30)
            print("⚠️  Limited access to public content only")
            print("📉 Reduced data quality and quantity")
            print("=" * 30)

        with LinkedInKeywordScraper(
            headless=args.headless,
            max_posts=args.max_posts,
            require_login=require_login,
            email=args.email,
            password=password
        ) as scraper:
            # Search for posts
            posts = scraper.search_posts_by_keywords(keywords, hours_back=args.hours)

            # Save results
            if args.format == 'csv':
                save_results_csv(posts, args.output)
            else:
                save_results_json(posts, args.output)

            # Print summary
            print_summary(posts, keywords, args.hours)

    except KeyboardInterrupt:
        logger.info("Search interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Error during search: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
