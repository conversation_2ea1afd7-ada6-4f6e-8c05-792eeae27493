#!/usr/bin/env python3
"""
Manual LinkedIn Scraper - Works with existing browser session
"""

import time
import json
import csv
import re
from datetime import datetime, timedelta
from playwright.sync_api import sync_playwright

def manual_linkedin_scraper():
    print("🔍 Manual LinkedIn Scraper")
    print("=" * 50)
    print("This approach works differently:")
    print("1. Opens a clean browser")
    print("2. You manually login and navigate to search")
    print("3. <PERSON><PERSON><PERSON> extracts data from the page you're viewing")
    print("=" * 50)
    
    with sync_playwright() as p:
        # Launch browser with minimal detection
        browser = p.chromium.launch(
            headless=False,
            args=[
                '--no-sandbox',
                '--disable-setuid-sandbox'
            ]
        )
        
        page = browser.new_page()
        
        try:
            # Step 1: Go to LinkedIn
            print("\n1. Opening LinkedIn...")
            page.goto("https://www.linkedin.com")
            time.sleep(3)
            
            # Step 2: Manual login and navigation
            print("\n2. Please complete these steps manually:")
            print("   a) Login to your LinkedIn account")
            print("   b) Search for 'KYC' in the search box")
            print("   c) Click 'Posts' filter")
            print("   d) Set time filter to 'Past 24 hours'")
            print("   e) Wait for results to load")
            
            input("\nPress Enter when you can see the KYC search results...")

            # Step 3: Scroll and extract data from page
            print("\n3. Scrolling through page to load all posts...")

            posts = extract_posts_with_scrolling(page)
            
            print(f"\n🎯 Found {len(posts)} posts")
            
            if posts:
                print("\n📝 Sample posts:")
                for i, post in enumerate(posts[:3]):
                    print(f"\n{i+1}. Poster: {post.get('poster_name', 'Unknown')}")
                    print(f"    Bio: {post.get('bio', 'Not found')}")
                    print(f"    Content: {post['content'][:100]}...")
                    print(f"    URL: {post.get('post_url', 'Not found')}")

                # Save results to CSV with your requested format including Bio column
                csv_filename = "linkedin_kyc_results.csv"
                with open(csv_filename, "w", newline='', encoding='utf-8') as csvfile:
                    fieldnames = ['Posters Name', 'Bio', 'Content of Post', 'Post URL']
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                    # Write header
                    writer.writeheader()

                    # Write data
                    for post in posts:
                        writer.writerow({
                            'Posters Name': post.get('poster_name', 'Unknown'),
                            'Bio': post.get('bio', ''),
                            'Content of Post': post.get('content', ''),
                            'Post URL': post.get('post_url', 'Not found')
                        })

                print(f"\n💾 Results saved to {csv_filename}")
                print(f"📊 CSV contains {len(posts)} posts with columns:")
                print("   1. Posters Name")
                print("   2. Bio")
                print("   3. Content of Post")
                print("   4. Post URL")

                # Also save JSON backup
                with open("manual_kyc_results.json", "w") as f:
                    json.dump(posts, f, indent=2)
                print(f"📄 JSON backup saved to manual_kyc_results.json")
            else:
                print("\n❌ No posts extracted. Let's debug...")
                debug_page_content(page)
            
            print("\nKeeping browser open for 30 seconds for inspection...")
            time.sleep(30)
            
        except Exception as e:
            print(f"\n❌ Error: {e}")
        finally:
            browser.close()

def extract_posts_with_scrolling(page):
    """Extract posts while scrolling through the page to load more content"""
    all_posts = []
    seen_urls = set()  # Track URLs to avoid duplicates
    scroll_attempts = 0
    max_scrolls = 10  # Maximum number of scrolls

    print("📜 Starting to scroll and extract posts...")

    while scroll_attempts < max_scrolls:
        # Extract posts from current view
        current_posts = extract_posts_from_page(page)

        # Add new posts (avoid duplicates)
        new_posts_count = 0
        for post in current_posts:
            post_url = post.get('post_url', '')
            if post_url and post_url not in seen_urls:
                seen_urls.add(post_url)
                all_posts.append(post)
                new_posts_count += 1
            elif not post_url:  # If no URL, check content to avoid duplicates
                content_key = post.get('content', '')[:100]  # First 100 chars as key
                if content_key not in [p.get('content', '')[:100] for p in all_posts]:
                    all_posts.append(post)
                    new_posts_count += 1

        print(f"   Scroll {scroll_attempts + 1}: Found {new_posts_count} new posts (Total: {len(all_posts)})")

        # If no new posts found, try scrolling a bit more
        if new_posts_count == 0:
            scroll_attempts += 1
            if scroll_attempts >= 3:  # Stop if no new posts for 3 scrolls
                print("   No new posts found, stopping scroll")
                break
        else:
            scroll_attempts = 0  # Reset counter if we found new posts

        # Scroll down to load more content
        print(f"   Scrolling down to load more posts...")
        page.evaluate("window.scrollBy(0, 800)")  # Scroll down 800px
        time.sleep(2)  # Wait for content to load

        # Also try scrolling to bottom to trigger infinite scroll
        if scroll_attempts % 3 == 0:  # Every 3rd attempt, scroll to bottom
            page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            time.sleep(3)

    print(f"📊 Scrolling complete. Total posts extracted: {len(all_posts)}")
    return all_posts

def extract_posts_from_page(page):
    """Extract posts from the current LinkedIn page with poster name and URL"""
    posts = []

    # Enhanced selectors to capture all types of posts including reposts
    selectors_to_try = [
        # Primary search result selectors
        '.search-results-container .reusable-search__result',
        '.search-result',
        '.reusable-search__result',

        # Feed selectors (for logged-in users)
        '.feed-shared-update-v2',
        '.update-components-actor',

        # Repost and shared content selectors
        '.feed-shared-update-v2--minimal-padding',
        '.feed-shared-update-v2[data-urn]',
        'div[data-urn*="share"]',
        'div[data-urn*="reshare"]',

        # Article and content containers
        'article',
        'article[data-urn]',
        'div[data-urn]',
        'div[data-id]',
        'li[data-urn]',

        # Generic containers
        'div[class*="update"]',
        'div[class*="post"]',
        'div[class*="activity"]',
        'div[class*="share"]'
    ]

    for selector in selectors_to_try:
        elements = page.query_selector_all(selector)
        if elements:
            print(f"✅ Found {len(elements)} elements with selector: {selector}")

            for element in elements:
                try:
                    # Try to extract just the post content first
                    post_content = extract_actual_post_content(element)

                    # If we can't find specific post content, fall back to full text
                    if not post_content:
                        text = element.inner_text().strip()
                        if not (text and len(text) > 50 and 'KYC' in text.upper()):
                            continue
                    else:
                        text = post_content
                        if not ('KYC' in text.upper()):
                            continue

                    # Extract poster name
                    poster_name = extract_poster_name(element)

                    # Extract bio/title
                    bio = extract_poster_bio(element)

                    # Extract post URL
                    post_url = extract_post_url(element, page)

                    # Clean up content (remove UI elements and bio info)
                    clean_content = clean_post_content(text, poster_name, bio)

                    # Only add if we have meaningful content after cleaning
                    if clean_content and len(clean_content) > 10:
                        posts.append({
                            'poster_name': poster_name,
                            'bio': bio,
                            'content': clean_content,
                            'post_url': post_url,
                            'selector_used': selector,
                            'extracted_at': datetime.now().isoformat()
                        })
                except Exception as e:
                    print(f"Error extracting post: {e}")
                    continue

            if posts:
                break

    return posts

def extract_actual_post_content(element):
    """Extract just the actual post content, including reposts and shared content"""
    # Try to find specific post content selectors first
    content_selectors = [
        # Primary post content selectors
        '.feed-shared-text__text-view',
        '.update-components-text',
        '.feed-shared-update-v2__description-wrapper .feed-shared-text',
        '.feed-shared-inline-show-more-text',

        # Repost and shared content selectors
        '.feed-shared-update-v2__content',
        '.feed-shared-update-v2__description',
        '.update-components-text .break-words',
        '.feed-shared-text',

        # Search result specific content
        '.search-result__primary-info .search-result__summary',
        '.search-result__summary',
        '.reusable-search__result-text',
        '.search-result__info',

        # Additional content areas
        'div[data-test-id="main-feed-activity-card__commentary"]',
        '.update-components-header + div',  # Content after header
        '.feed-shared-update-v2__content .feed-shared-text',

        # Fallback content selectors
        '.ember-view .break-words',
        'span[dir="ltr"]'
    ]

    # Try each selector and collect all content
    all_content = []

    for selector in content_selectors:
        try:
            content_elements = element.query_selector_all(selector)
            for content_element in content_elements:
                content = content_element.inner_text().strip()
                if content and len(content) > 20:  # Ensure meaningful content
                    # Avoid duplicates
                    if content not in all_content:
                        all_content.append(content)
        except:
            continue

    # Return the longest content piece (likely the main post)
    if all_content:
        return max(all_content, key=len)

    return ""

def extract_poster_name(element):
    """Extract the poster's name from a post element, handling reposts"""
    name_selectors = [
        # Primary poster name selectors
        '.update-components-actor__name',
        '.feed-shared-actor__name',
        '.search-result__info .actor-name',
        '.entity-result__title-text a span[aria-hidden="true"]',
        'a[data-control-name="actor"] span[aria-hidden="true"]',
        '.actor-name',

        # Repost specific selectors (person who shared)
        '.feed-shared-actor__name a span[aria-hidden="true"]',
        '.update-components-actor__name a span[aria-hidden="true"]',

        # Search result selectors
        '.search-result__info a span[aria-hidden="true"]',
        '.entity-result__title-text span[aria-hidden="true"]',

        # Generic selectors
        'span[dir="ltr"] span[aria-hidden="true"]',
        'a[href*="/in/"] span[aria-hidden="true"]',

        # Fallback selectors
        'a[href*="/in/"] span',
        'span[aria-hidden="true"]'
    ]

    # Try to get both the reposter and original poster
    names_found = []

    for selector in name_selectors:
        try:
            name_elements = element.query_selector_all(selector)
            for name_element in name_elements:
                name = name_element.inner_text().strip()
                if name and len(name) > 1 and len(name) < 100:  # Reasonable name length
                    if name not in names_found:
                        names_found.append(name)
        except:
            continue

    # Return the first valid name found, or indicate repost if multiple names
    if len(names_found) > 1:
        return f"{names_found[0]} (reposted from {names_found[1]})"
    elif len(names_found) == 1:
        return names_found[0]

    return "Unknown"

def extract_poster_bio(element):
    """Extract the poster's bio/title from a post element"""
    bio_selectors = [
        # Common LinkedIn bio/title selectors
        '.update-components-actor__description',
        '.feed-shared-actor__description',
        '.search-result__info .actor-description',
        '.entity-result__primary-subtitle',
        '.actor-description',
        '.update-components-actor__sub-description',
        '.feed-shared-actor__sub-description',

        # Fallback selectors for professional titles
        'div[class*="description"]',
        'span[class*="subtitle"]'
    ]

    for selector in bio_selectors:
        try:
            bio_element = element.query_selector(selector)
            if bio_element:
                bio = bio_element.inner_text().strip()
                # Filter out time stamps and other non-bio content
                if bio and len(bio) > 3 and len(bio) < 200 and not any(time_word in bio.lower() for time_word in ['ago', 'hour', 'day', 'week', 'month', 'min']):
                    return bio
        except:
            continue

    return ""

def extract_post_url(element, page):
    """Extract the post URL from a post element"""
    url_selectors = [
        # Direct post links
        'a[href*="/feed/update/"]',
        'a[href*="/posts/"]',
        '.update-components-actor__sub-description-link',
        'a[href*="linkedin.com/posts"]',

        # Search result links
        '.search-result__info a[href*="/posts/"]',
        'a[href*="activity"]',

        # Generic LinkedIn links
        'a[href*="linkedin.com"]'
    ]

    for selector in url_selectors:
        try:
            link_element = element.query_selector(selector)
            if link_element:
                href = link_element.get_attribute('href')
                if href:
                    # Convert relative URLs to absolute
                    if href.startswith('/'):
                        return f"https://www.linkedin.com{href}"
                    elif 'linkedin.com' in href:
                        return href
        except:
            continue

    return "Not found"

def clean_post_content(text, poster_name="", bio=""):
    """Clean up post content by removing UI elements, poster name, and bio"""
    import re

    cleaned = text

    # Remove poster name if it appears (case insensitive, multiple occurrences)
    if poster_name:
        cleaned = re.sub(re.escape(poster_name), '', cleaned, flags=re.IGNORECASE)

    # Remove bio if it appears (case insensitive, multiple occurrences)
    if bio:
        cleaned = re.sub(re.escape(bio), '', cleaned, flags=re.IGNORECASE)

    # Remove common LinkedIn UI elements and phrases
    ui_patterns = [
        r'Feed post\s*',
        r'Visible to anyone on or off LinkedIn\s*',
        r'View profile\s*',
        r'Follow\s*',
        r'Connect\s*',
        r'Message\s*',
        r'Like\s*',
        r'Comment\s*',
        r'Share\s*',
        r'Send\s*',
        r'Copy\s*',
        r'Report\s*',
        r'Save\s*',
        r'Repost\s*',
        r'React\s*',
        r'See more\s*',
        r'See less\s*',
        r'Show more\s*',
        r'Show less\s*',
        r'More\s*',
        r'🔄\s*',
        r'•\s*•\s*',
        r'•\s+',
        r'\+\s*\d+(st|nd|rd|th)\+?\s*',  # Remove "+3rd+" type indicators
    ]

    for pattern in ui_patterns:
        cleaned = re.sub(pattern, ' ', cleaned, flags=re.IGNORECASE)

    # Remove connection degree indicators (1st, 2nd, 3rd)
    cleaned = re.sub(r'•?\s*(1st|2nd|3rd)\+?\s*', ' ', cleaned, flags=re.IGNORECASE)

    # Remove time stamps (like "2h", "1d", "3w ago")
    cleaned = re.sub(r'\b\d+[hdwm]\b', '', cleaned)
    cleaned = re.sub(r'\b\d+\s*(hour|day|week|month)s?\s*ago\b', '', cleaned, flags=re.IGNORECASE)

    # Remove repeated phrases (common in LinkedIn where bio appears multiple times)
    words = cleaned.split()
    if len(words) > 10:  # Only do this for longer text
        # Find and remove repeated sequences
        cleaned_words = []
        i = 0
        while i < len(words):
            # Check for repeated sequences of 3+ words
            sequence_found = False
            for seq_len in range(min(10, len(words) - i), 2, -1):  # Check sequences from 10 words down to 3
                if i + seq_len * 2 <= len(words):
                    seq1 = words[i:i + seq_len]
                    seq2 = words[i + seq_len:i + seq_len * 2]
                    if seq1 == seq2:  # Found repeated sequence
                        cleaned_words.extend(seq1)  # Add it once
                        i += seq_len * 2  # Skip both occurrences
                        sequence_found = True
                        break

            if not sequence_found:
                cleaned_words.append(words[i])
                i += 1

        cleaned = ' '.join(cleaned_words)

    # Remove extra whitespace and clean up
    cleaned = ' '.join(cleaned.split())

    # Remove leading/trailing punctuation that might be left over
    cleaned = cleaned.strip('.,;:!?-•+|')

    # If the cleaned content is too short or seems to be just profile info, return empty
    if len(cleaned) < 20 or all(word in bio + poster_name for word in cleaned.split()[:5]):
        return ""

    return cleaned.strip()

def debug_page_content(page):
    """Debug what's actually on the page"""
    try:
        # Get page title
        title = page.title()
        print(f"Page title: {title}")
        
        # Get page URL
        url = page.url
        print(f"Current URL: {url}")
        
        # Get page text
        body_text = page.inner_text('body')
        print(f"Page content length: {len(body_text)} characters")
        
        # Check for key terms
        key_terms = ['KYC', 'search', 'results', 'posts', 'no results', 'sign in']
        for term in key_terms:
            if term.lower() in body_text.lower():
                print(f"✅ Found '{term}' in page content")
            else:
                print(f"❌ '{term}' not found in page content")
        
        # Show sample content
        print(f"\nFirst 300 characters of page:")
        print("-" * 50)
        print(body_text[:300])
        print("-" * 50)
        
        # Take screenshot
        page.screenshot(path="debug_manual_scraper.png")
        print("\n📸 Screenshot saved as debug_manual_scraper.png")
        
    except Exception as e:
        print(f"Debug error: {e}")

if __name__ == "__main__":
    manual_linkedin_scraper()
