#!/usr/bin/env python3
"""
LinkedIn Browser Module - Safe browser automation for LinkedIn
"""

import logging
import random
import re
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Union
from urllib.parse import urlparse

from playwright.sync_api import sync_playwright, <PERSON>, <PERSON><PERSON><PERSON>, BrowserContext

logger = logging.getLogger(__name__)

class LinkedInBrowser:
    """
    A browser automation class for LinkedIn that implements safety measures
    to avoid account flagging.
    """

    # LinkedIn URLs
    BASE_URL = "https://www.linkedin.com"
    LOGIN_URL = f"{BASE_URL}/login"

    # Safety parameters
    MIN_DELAY = 2  # Minimum delay between actions in seconds
    MAX_DELAY = 7  # Maximum delay between actions in seconds
    SCROLL_DELAY = 0.5  # Delay during scrolling in seconds
    SCROLL_STEP = 300  # Pixels to scroll each step

    def __init__(self, headless: bool = False, force_visible: bool = False):
        """
        Initialize the LinkedIn browser.

        Args:
            headless: Whether to run the browser in headless mode.
                     Not recommended for login.
            force_visible: Force the browser to be visible by using specific
                          launch options. Use if browser window doesn't appear.
        """
        self.headless = headless
        self.force_visible = force_visible
        self.playwright = None
        self.browser = None
        self.context = None
        self.page = None

    def __enter__(self):
        """Context manager entry point."""
        self.start()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit point."""
        self.close()

    def start(self):
        """Start the browser and navigate to LinkedIn."""
        logger.info("Starting browser")

        # Initialize Playwright
        self.playwright = sync_playwright().start()

        # Common browser arguments
        browser_args = [
            '--disable-blink-features=AutomationControlled',
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-infobars',
            '--window-size=1280,800',
            '--disable-extensions',
        ]

        # Add special arguments to force visibility if requested
        if self.force_visible:
            browser_args.extend([
                '--start-maximized',
                '--auto-open-devtools-for-tabs',  # This forces the window to be visible
                '--new-window',
            ])
            logger.info("Using force-visible mode with special browser arguments")

        # Try to launch Chrome first, fall back to default Chromium or Firefox if not available
        try:
            logger.info("Attempting to launch Chrome...")
            self.browser = self.playwright.chromium.launch(
                headless=self.headless,
                args=browser_args,
                channel="chrome"  # Use installed Chrome
            )
            logger.info("Chrome launched successfully")
        except Exception as e:
            logger.warning(f"Failed to launch Chrome: {e}")
            try:
                logger.info("Attempting to launch Chromium...")
                self.browser = self.playwright.chromium.launch(
                    headless=self.headless,
                    args=browser_args
                )
                logger.info("Chromium launched successfully")
            except Exception as e2:
                logger.warning(f"Failed to launch Chromium: {e2}")
                logger.info("Attempting to launch Firefox as fallback...")
                # Firefox doesn't support the same args, so we use a simpler config
                firefox_args = ['--width=1280', '--height=800']
                if self.force_visible:
                    firefox_args.append('--devtools')
                self.browser = self.playwright.firefox.launch(
                    headless=self.headless,
                    args=firefox_args
                )
                logger.info("Firefox launched successfully")

        # Create a context with human-like characteristics
        self.context = self.browser.new_context(
            viewport={'width': 1280, 'height': 800},
            user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
            locale='en-US',
            timezone_id='America/New_York',
            has_touch=False,
            is_mobile=False,
        )

        # Add random fingerprint to avoid detection
        self._randomize_fingerprint()

        # Create a new page
        self.page = self.context.new_page()

        # Navigate to LinkedIn
        self.page.goto(self.BASE_URL)
        self._random_delay()

        return self

    def close(self):
        """Close the browser."""
        if self.browser:
            logger.info("Closing browser")
            self.browser.close()
            self.playwright.stop()
            self.browser = None
            self.context = None
            self.page = None

    def wait_for_login(self):
        """
        Wait for manual login to LinkedIn.

        This is a safety measure to avoid storing credentials.
        """
        if not self.page:
            raise RuntimeError("Browser not started")

        # Navigate to login page
        logger.info("Navigating to LinkedIn login page...")
        self.page.goto(self.LOGIN_URL)

        logger.info("Waiting for manual login...")
        print("\n" + "="*70)
        print("MANUAL LOGIN REQUIRED")
        print("A browser window should be opening now. If you don't see it, check your taskbar.")
        print("Please log in to your LinkedIn account in the browser window")
        print("This script will continue once you're logged in")
        print("If no browser window appears, try running with: --headless False")
        print("="*70 + "\n")

        # Wait for navigation to feed which indicates successful login
        self.page.wait_for_url(f"{self.BASE_URL}/feed/**", timeout=300000)  # 5 minutes timeout

        logger.info("Login successful")
        self._random_delay()

    def visit_profile(self, url: str) -> Optional[Dict]:
        """
        Visit a LinkedIn profile and extract basic information.

        Args:
            url: LinkedIn profile URL

        Returns:
            Dictionary with profile information or None if failed
        """
        if not self.page:
            raise RuntimeError("Browser not started")

        # Validate URL
        if not self._is_valid_linkedin_url(url):
            logger.warning(f"Invalid LinkedIn URL: {url}")
            return None

        logger.info(f"Visiting profile: {url}")

        try:
            # Navigate to profile
            self.page.goto(url)
            self._random_delay()

            # Scroll gently to load content
            self._human_like_scroll()

            # Extract profile information
            profile_data = {
                'url': url,
                'name': self._extract_text('.pv-top-card-section__name, .text-heading-xlarge'),
                'title': self._extract_text('.pv-top-card-section__headline, .text-body-medium'),
                'company': self._extract_text('.pv-top-card-v2-section__company-name, .pv-text-details__right-panel-item'),
                'location': self._extract_text('.pv-top-card-section__location, .text-body-small.inline.t-black--light.break-words'),
                'extracted_at': datetime.now().isoformat()
            }

            return profile_data

        except Exception as e:
            logger.error(f"Error visiting profile {url}: {e}")
            return None

    def get_recent_posts(self, profile_url: str, date_threshold: datetime) -> List[Dict]:
        """
        Get recent posts from a LinkedIn profile, focusing on posts shared by the user.

        Args:
            profile_url: LinkedIn profile URL
            date_threshold: Date threshold for recent posts (e.g., 14 days ago)

        Returns:
            List of dictionaries with post information, focusing on text content
        """
        if not self.page:
            raise RuntimeError("Browser not started")

        # Try different activity URLs (LinkedIn changes these paths occasionally)
        activity_urls = [
            f"{profile_url}/recent-activity/shares/",
            f"{profile_url}/detail/recent-activity/shares/",
            f"{profile_url}/posts/",
            f"{profile_url}/recent-activity/all/"
        ]

        posts = []

        # Try each URL until we find posts
        for activity_url in activity_urls:
            logger.info(f"Trying to get posts from: {activity_url}")

            try:
                # Navigate to activity section
                self.page.goto(activity_url)

                # Wait longer for content to load
                logger.info("Waiting for content to load...")
                self._random_delay(min_delay=3, max_delay=5)

                # Wait for post elements to appear
                try:
                    self.page.wait_for_selector('div.update-components-actor, div.feed-shared-update-v2, article.ember-view', timeout=10000)
                except Exception:
                    logger.info("No post elements found with primary selectors, trying alternative selectors...")
                    try:
                        self.page.wait_for_selector('div.occludable-update, div.feed-shared-update, .artdeco-card', timeout=5000)
                    except Exception:
                        logger.warning(f"No posts found at {activity_url}")
                        continue

                # Scroll more to load content (LinkedIn lazy-loads posts)
                logger.info("Scrolling to load more posts...")
                self._human_like_scroll(max_scrolls=10, scroll_delay=1.0)

                # Try multiple selectors for post elements (LinkedIn changes these frequently)
                post_selectors = [
                    'div.update-components-actor',  # Newer LinkedIn
                    'div.feed-shared-update-v2',    # Newer LinkedIn
                    'article.ember-view',           # Newer LinkedIn
                    'div.occludable-update',        # Older LinkedIn
                    'div.feed-shared-update',       # Older LinkedIn
                    '.artdeco-card'                 # Generic fallback
                ]

                # Try each selector
                for selector in post_selectors:
                    post_elements = self.page.query_selector_all(selector)
                    if post_elements and len(post_elements) > 0:
                        logger.info(f"Found {len(post_elements)} posts with selector: {selector}")
                        break

                if not post_elements or len(post_elements) == 0:
                    logger.warning(f"No posts found at {activity_url} with any selector")
                    continue

                # Extract posts
                for post_element in post_elements:
                    try:
                        # Try multiple selectors for date
                        date_selectors = [
                            '.update-components-actor__sub-description',  # Newer LinkedIn
                            '.feed-shared-actor__sub-description',        # Older LinkedIn
                            '.feed-shared-actor__sub-description span',   # Alternative
                            'time',                                       # Generic time element
                            '.artdeco-entity-lockup__caption',            # Another alternative
                            'span.visually-hidden'                        # Hidden timestamp
                        ]

                        date_text = None
                        for date_selector in date_selectors:
                            date_element = post_element.query_selector(date_selector)
                            if date_element:
                                date_text = date_element.inner_text().strip()
                                if date_text:
                                    logger.debug(f"Found date text: {date_text}")
                                    break

                        if not date_text:
                            logger.debug("Could not find date for post, skipping")
                            continue

                        # Parse the date
                        post_date = self._parse_linkedin_date(date_text)

                        # Skip if post is older than threshold
                        if post_date and post_date < date_threshold:
                            logger.debug(f"Post from {post_date.isoformat()} is older than threshold {date_threshold.isoformat()}, skipping")
                            continue

                        # Determine if this is a shared post and get shared content URL
                        is_shared = False
                        shared_content_url = ""
                        shared_indicators = [
                            '.feed-shared-update-v2__reshare-context',
                            '.update-components-header__text',
                            'span:has-text("shared")',
                            'span:has-text("reposted")',
                            '.update-components-actor__description:has-text("shared")',
                            '.update-components-actor__description:has-text("reposted")',
                            '.feed-shared-update-v2__content-wrapper',
                            '.update-components-article',
                            '.feed-shared-article'
                        ]

                        for indicator in shared_indicators:
                            if post_element.query_selector(indicator):
                                is_shared = True
                                logger.debug("Detected a shared/reposted content")

                                # Try to extract the URL of the shared content
                                shared_content_url = self._extract_shared_content_url(post_element)
                                break

                        # Extract the user's commentary on shared posts
                        user_commentary = ""
                        commentary_selectors = [
                            '.feed-shared-update-v2__commentary',
                            '.update-components-text',
                            '.update-components-commentary',
                            'div[data-test-id="main-feed-activity-card__commentary"]'
                        ]

                        for selector in commentary_selectors:
                            commentary_element = post_element.query_selector(selector)
                            if commentary_element:
                                user_commentary = commentary_element.inner_text().strip()
                                if user_commentary:
                                    logger.debug(f"Found user commentary: {user_commentary[:50]}...")
                                    break

                        # Try multiple selectors for the main content
                        content_selectors = [
                            '.update-components-text',                      # Newer LinkedIn
                            '.feed-shared-update-v2__description-wrapper',  # Older LinkedIn
                            '.feed-shared-text',                            # Older LinkedIn
                            '.feed-shared-text__text-view',                 # Another alternative
                            '.feed-shared-inline-show-more-text',           # Expandable text
                            'div[data-test-id="main-feed-activity-card__commentary"]',  # Another newer format
                            '.feed-shared-update-v2__content',              # Content container
                            '.feed-shared-article__description',            # Article description
                            '.feed-shared-external-article__description',   # External article
                            '.feed-shared-text__text-view',                 # Text view
                            'span.break-words'                              # Generic text
                        ]

                        main_content = ""
                        for content_selector in content_selectors:
                            content_element = post_element.query_selector(content_selector)
                            if content_element:
                                main_content = content_element.inner_text().strip()
                                if main_content:
                                    logger.debug(f"Found main content with selector {content_selector}: {main_content[:50]}...")
                                    break

                        # For shared posts, try to get the original post content
                        shared_content = ""
                        if is_shared:
                            shared_content_selectors = [
                                '.feed-shared-update-v2__description',
                                '.feed-shared-text__text-view',
                                '.update-components-article',
                                '.update-components-image',
                                '.update-components-text',
                                '.feed-shared-article__description',
                                '.feed-shared-external-article__description'
                            ]

                            for selector in shared_content_selectors:
                                shared_element = post_element.query_selector(selector)
                                if shared_element:
                                    shared_content = shared_element.inner_text().strip()
                                    if shared_content:
                                        logger.debug(f"Found shared content: {shared_content[:50]}...")
                                        break

                        # Combine the content based on what we found
                        if is_shared:
                            if user_commentary and shared_content:
                                content = f"User commentary: {user_commentary}\n\nShared content: {shared_content}"
                            elif user_commentary:
                                content = f"User commentary: {user_commentary}"
                            elif shared_content:
                                content = f"Shared content: {shared_content}"
                            else:
                                content = main_content
                        else:
                            content = main_content

                        # If still no content found, try to get any text from the post
                        if not content:
                            content = post_element.inner_text().strip()
                            # Remove common UI elements text
                            content = re.sub(r'Like|Comment|Share|Send|Copy|Report|Save', '', content)
                            # Limit to first 2000 characters if it's too long (might be capturing too much)
                            if len(content) > 2000:
                                content = content[:2000] + "..."

                        # Try to extract post type (text, article, image, video, etc.)
                        post_type = "text"  # Default type

                        # Check for article
                        if post_element.query_selector('.feed-shared-article, .update-components-article, .article-card'):
                            post_type = "article"
                        # Check for image
                        elif post_element.query_selector('.feed-shared-image, .update-components-image, img.update-components-image__image'):
                            post_type = "image"
                        # Check for video
                        elif post_element.query_selector('.feed-shared-video, .update-components-video, video'):
                            post_type = "video"
                        # Check for document
                        elif post_element.query_selector('.feed-shared-document, .update-components-document'):
                            post_type = "document"
                        # Check for poll
                        elif post_element.query_selector('.feed-shared-poll, .update-components-poll'):
                            post_type = "poll"

                        # Add post to results with enhanced metadata
                        post_data = {
                            'date': post_date.isoformat() if post_date else None,
                            'content': content,
                            'url': self._extract_post_url(post_element),
                            'shared_content_url': shared_content_url if is_shared else None,
                            'type': post_type,
                            'is_shared': is_shared,
                            'has_user_commentary': bool(user_commentary)
                        }

                        # Only add if we have both date and content, and it's either:
                        # 1. An original post by the user, OR
                        # 2. A shared post where the user added commentary
                        should_include = False

                        if post_data['date'] and post_data['content']:
                            if not is_shared:
                                # Original post by the user
                                should_include = True
                                logger.debug("Including original post by user")
                            elif is_shared and user_commentary:
                                # Shared post with user commentary
                                should_include = True
                                logger.debug("Including shared post with user commentary")
                            else:
                                # Shared post without user commentary - skip
                                logger.debug("Skipping shared post without user commentary")

                        if should_include:
                            posts.append(post_data)
                            post_description = "shared" if is_shared else "original"
                            logger.info(f"Found {post_description} {post_type} post from {post_data['date']} with {len(post_data['content'])} characters")

                    except Exception as e:
                        logger.error(f"Error extracting post: {e}")

                # If we found posts, no need to try other URLs
                if posts:
                    logger.info(f"Found {len(posts)} posts at {activity_url}")
                    break

            except Exception as e:
                logger.error(f"Error getting posts from {activity_url}: {e}")

        if not posts:
            logger.warning("No posts found for this profile")

        return posts

    def _random_delay(self, min_delay: float = None, max_delay: float = None):
        """
        Add a random delay between actions to mimic human behavior.

        Args:
            min_delay: Minimum delay in seconds (overrides self.MIN_DELAY if provided)
            max_delay: Maximum delay in seconds (overrides self.MAX_DELAY if provided)
        """
        min_delay = min_delay if min_delay is not None else self.MIN_DELAY
        max_delay = max_delay if max_delay is not None else self.MAX_DELAY

        delay = random.uniform(min_delay, max_delay)
        logger.debug(f"Random delay: {delay:.2f} seconds")
        time.sleep(delay)

    def _human_like_scroll(self, max_scrolls: int = 10, scroll_delay: float = None):
        """
        Scroll the page in a human-like manner.

        Args:
            max_scrolls: Maximum number of scroll steps
            scroll_delay: Delay during scrolling in seconds (overrides self.SCROLL_DELAY if provided)
        """
        if not self.page:
            return

        scroll_delay = scroll_delay if scroll_delay is not None else self.SCROLL_DELAY

        # Get page height
        height = self.page.evaluate('() => document.body.scrollHeight')
        logger.debug(f"Page height: {height}px, will scroll up to {max_scrolls} times")

        # Scroll down gradually with random pauses
        for i in range(min(max_scrolls, max(5, height // self.SCROLL_STEP))):
            # Random scroll amount
            scroll_amount = random.randint(self.SCROLL_STEP - 50, self.SCROLL_STEP + 50)

            # Scroll with a smooth behavior
            self.page.evaluate(f'window.scrollBy(0, {scroll_amount})')
            logger.debug(f"Scroll {i+1}/{max_scrolls}: {scroll_amount}px")

            # Random pause
            pause_time = random.uniform(scroll_delay, scroll_delay * 2)
            time.sleep(pause_time)

            # Occasionally pause longer to mimic reading
            if random.random() < 0.2:
                reading_pause = random.uniform(1, 3)
                logger.debug(f"Reading pause: {reading_pause:.2f} seconds")
                time.sleep(reading_pause)

            # Check if we've reached the bottom of the page
            scrolled_height = self.page.evaluate('() => window.pageYOffset + window.innerHeight')
            total_height = self.page.evaluate('() => document.body.scrollHeight')
            if scrolled_height >= total_height - 200:  # Within 200px of the bottom
                logger.debug("Reached bottom of page")

                # Wait a bit to see if more content loads
                time.sleep(2)

                # Check if the height changed (more content loaded)
                new_height = self.page.evaluate('() => document.body.scrollHeight')
                if new_height <= total_height:
                    logger.debug("No more content loaded, stopping scroll")
                    break

        # Scroll back up a bit to mimic natural behavior
        self.page.evaluate('window.scrollBy(0, -300)')
        logger.debug("Scrolled back up 300px to mimic natural behavior")

    def _extract_text(self, selector: str) -> str:
        """
        Extract text from an element.

        Args:
            selector: CSS selector

        Returns:
            Extracted text or empty string
        """
        if not self.page:
            return ""

        element = self.page.query_selector(selector)
        if element:
            return element.inner_text().strip()
        return ""

    def _extract_shared_content_url(self, post_element) -> Optional[str]:
        """
        Extract the URL of shared/reposted content.

        Args:
            post_element: Post element

        Returns:
            Shared content URL or None
        """
        try:
            # Try multiple selectors for shared content links
            shared_link_selectors = [
                'a[href*="/posts/"]',  # Direct post links
                'a[href*="/feed/update/"]',  # Feed update links
                '.feed-shared-article a[href]',  # Article links
                '.feed-shared-external-article a[href]',  # External article links
                '.update-components-article a[href]',  # Article component links
                '.feed-shared-update-v2__content a[href]',  # Content wrapper links
                'a.app-aware-link[href*="linkedin.com"]',  # LinkedIn aware links
                '.feed-shared-text a[href]',  # Text content links
                '.update-components-text a[href]'  # Update component text links
            ]

            for selector in shared_link_selectors:
                links = post_element.query_selector_all(selector)
                for link in links:
                    href = link.get_attribute('href')
                    if href and ('linkedin.com' in href or href.startswith('/')):
                        # Skip profile links, focus on content links
                        if '/in/' not in href or '/posts/' in href or '/feed/update/' in href:
                            full_url = f"{self.BASE_URL}{href}" if href.startswith('/') else href
                            logger.debug(f"Found shared content URL: {full_url}")
                            return full_url

            # If no specific shared content URL found, try to get any external link
            external_links = post_element.query_selector_all('a[href^="http"]:not([href*="linkedin.com"])')
            if external_links:
                href = external_links[0].get_attribute('href')
                if href:
                    logger.debug(f"Found external shared content URL: {href}")
                    return href

        except Exception as e:
            logger.error(f"Error extracting shared content URL: {e}")

        return None

    def _extract_post_url(self, post_element) -> Optional[str]:
        """
        Extract the URL of a post.

        Args:
            post_element: Post element

        Returns:
            Post URL or None
        """
        try:
            # Try multiple selectors for post links
            link_selectors = [
                'a.app-aware-link[href*="/feed/update/"]',
                'a[href*="/feed/update/"]',
                'a[href*="/posts/"]',
                'a.feed-shared-update-v2__permalink',
                'a.update-components-actor__sub-description-link',
                'a.update-components-actor__container-link',
                'a.artdeco-button[href*="/feed/update/"]'
            ]

            for selector in link_selectors:
                link = post_element.query_selector(selector)
                if link:
                    href = link.get_attribute('href')
                    if href:
                        full_url = f"{self.BASE_URL}{href}" if href.startswith('/') else href
                        logger.debug(f"Found post URL: {full_url}")
                        return full_url

            # If no specific link found, try to find any link that might be a post
            all_links = post_element.query_selector_all('a')
            for link in all_links:
                href = link.get_attribute('href')
                if href and ('/feed/update/' in href or '/posts/' in href):
                    full_url = f"{self.BASE_URL}{href}" if href.startswith('/') else href
                    logger.debug(f"Found post URL from generic link: {full_url}")
                    return full_url

        except Exception as e:
            logger.error(f"Error extracting post URL: {e}")

        return None

    def _parse_linkedin_date(self, date_str: str) -> Optional[datetime]:
        """
        Parse LinkedIn date format.

        Args:
            date_str: Date string from LinkedIn

        Returns:
            Datetime object or None
        """
        try:
            # Clean up the date string
            date_str = date_str.lower().strip()
            logger.debug(f"Parsing date string: '{date_str}'")

            # Remove any prefix like "Posted: " or "Shared: "
            if ':' in date_str and not date_str[0].isdigit():
                date_str = date_str.split(':', 1)[1].strip()

            now = datetime.now()

            # Handle relative time formats
            if any(term in date_str for term in ['now', 'just now', 'few seconds', 'moments']):
                return now

            # Extract number and unit from relative time strings
            time_patterns = [
                (r'(\d+)\s*(?:minute|min)s?', 'minutes'),
                (r'(\d+)\s*(?:hour|hr)s?', 'hours'),
                (r'(\d+)\s*(?:day)s?', 'days'),
                (r'(\d+)\s*(?:week|wk)s?', 'weeks'),
                (r'(\d+)\s*(?:month|mo)s?', 'months'),
                (r'(\d+)\s*(?:year|yr)s?', 'years')
            ]

            for pattern, unit in time_patterns:
                match = re.search(pattern, date_str)
                if match:
                    value = int(match.group(1))
                    logger.debug(f"Matched relative time: {value} {unit}")

                    if unit == 'minutes':
                        return now - timedelta(minutes=value)
                    elif unit == 'hours':
                        return now - timedelta(hours=value)
                    elif unit == 'days':
                        return now - timedelta(days=value)
                    elif unit == 'weeks':
                        return now - timedelta(weeks=value)
                    elif unit == 'months':
                        # Approximate
                        return now - timedelta(days=value * 30)
                    elif unit == 'years':
                        # Approximate
                        return now - timedelta(days=value * 365)

            # Handle "yesterday" and "today"
            if 'yesterday' in date_str:
                return now - timedelta(days=1)
            if 'today' in date_str:
                return now

            # Try various date formats
            date_formats = [
                '%b %d, %Y',           # May 23, 2025
                '%B %d, %Y',           # May 23, 2025
                '%d %b %Y',            # 23 May 2025
                '%d %B %Y',            # 23 May 2025
                '%Y-%m-%d',            # 2025-05-23
                '%m/%d/%Y',            # 05/23/2025
                '%d/%m/%Y',            # 23/05/2025
                '%b %d',               # May 23 (assume current year)
                '%B %d'                # May 23 (assume current year)
            ]

            for date_format in date_formats:
                try:
                    # For formats without year, add the current year
                    if '%Y' not in date_format:
                        parsed_date = datetime.strptime(date_str, date_format)
                        parsed_date = parsed_date.replace(year=now.year)

                        # If the date is in the future, it's probably from last year
                        if parsed_date > now:
                            parsed_date = parsed_date.replace(year=now.year - 1)

                        logger.debug(f"Parsed date without year: {parsed_date}")
                        return parsed_date
                    else:
                        parsed_date = datetime.strptime(date_str, date_format)
                        logger.debug(f"Parsed date with format {date_format}: {parsed_date}")
                        return parsed_date
                except ValueError:
                    continue

            # If we get here, we couldn't parse the date
            logger.warning(f"Could not parse date string: '{date_str}'")

            # As a fallback, assume it's recent (within the last week)
            # This ensures we don't miss potentially relevant posts
            logger.debug("Using fallback date (1 week ago)")
            return now - timedelta(days=7)

        except Exception as e:
            logger.error(f"Error parsing date '{date_str}': {e}")
            return None

    def _is_valid_linkedin_url(self, url: str) -> bool:
        """
        Check if a URL is a valid LinkedIn profile URL.

        Args:
            url: URL to check

        Returns:
            True if valid, False otherwise
        """
        try:
            parsed = urlparse(url)
            return (
                parsed.netloc in ('www.linkedin.com', 'linkedin.com') and
                '/in/' in parsed.path
            )
        except Exception:
            return False

    def _randomize_fingerprint(self):
        """Add random fingerprint to avoid detection."""
        if not self.context:
            return

        # Override specific JavaScript properties that automation detection scripts check
        self.context.add_init_script("""
        () => {
            // Override navigator properties
            Object.defineProperty(navigator, 'webdriver', {
                get: () => false
            });

            // Override permissions
            Object.defineProperty(navigator, 'permissions', {
                get: () => {
                    return {
                        query: async () => {
                            return { state: 'prompt' };
                        }
                    };
                }
            });

            // Add random screen resolution
            Object.defineProperty(window, 'screen', {
                get: () => {
                    return {
                        width: Math.floor(Math.random() * 100) + 1920,
                        height: Math.floor(Math.random() * 100) + 1080,
                        availWidth: Math.floor(Math.random() * 100) + 1920,
                        availHeight: Math.floor(Math.random() * 100) + 1080,
                        colorDepth: 24,
                        pixelDepth: 24
                    };
                }
            });
        }
        """)
