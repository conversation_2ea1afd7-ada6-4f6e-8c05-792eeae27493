#!/usr/bin/env python3
"""
LinkedIn Profile Analyzer - Main Module
"""

import argparse
import os
import sys
import logging
from datetime import datetime, timedelta
import pandas as pd
from tqdm import tqdm

from linkedin_browser import LinkedInBrowser

# Try to import NLP utilities, but make them optional
try:
    from nlp_utils import extract_topics
    NLP_AVAILABLE = True
except ImportError:
    NLP_AVAILABLE = False
    def extract_topics(text, max_topics=5):
        """Fallback function when spaCy is not available"""
        return []

# Configure logging
def setup_logging(debug=False):
    """Configure logging with appropriate level."""
    level = logging.DEBUG if debug else logging.INFO

    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler("linkedin_analyzer.log"),
            logging.StreamHandler()
        ]
    )

    # Set Playwright's logger level
    playwright_level = logging.DEBUG if debug else logging.INFO
    logging.getLogger('playwright').setLevel(playwright_level)

    logger = logging.getLogger(__name__)
    logger.setLevel(level)

    if debug:
        logger.debug("Debug logging enabled")

    return logger

# Initialize logger with default level
logger = setup_logging()

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='LinkedIn Profile Analyzer')
    parser.add_argument('--input', '-i', type=str, required=True,
                        help='Path to file containing LinkedIn URLs (one per line)')
    parser.add_argument('--output', '-o', type=str, default='linkedin_results.csv',
                        help='Path to output CSV file')
    parser.add_argument('--text-output', type=str, default='',
                        help='Path to output text file for full post content (optional)')
    parser.add_argument('--days', '-d', type=int, default=14,
                        help='Number of days to look back for recent posts')
    parser.add_argument('--max-profiles', '-m', type=int, default=30,
                        help='Maximum number of profiles to visit in this session')
    parser.add_argument('--extract-topics', '-t', action='store_true',
                        help='Extract topics from posts using NLP (requires spaCy)')
    parser.add_argument('--headless', action='store_true',
                        help='Run browser in headless mode (not recommended for login)')
    parser.add_argument('--force-visible', action='store_true',
                        help='Force the browser to be visible (use if browser window does not appear)')
    parser.add_argument('--debug', action='store_true',
                        help='Enable debug logging for troubleshooting')
    parser.add_argument('--only-shared', action='store_true',
                        help='Only extract shared posts (with user commentary)')

    args = parser.parse_args()

    # Set up logging with debug level if requested
    global logger
    logger = setup_logging(args.debug)

    # Warn if topic extraction is requested but spaCy is not available
    if args.extract_topics and not NLP_AVAILABLE:
        print("WARNING: Topic extraction requested but spaCy is not installed.")
        print("To enable topic extraction, install spaCy with: pip install spacy")
        print("Then download the English model with: python -m spacy download en_core_web_sm")
        print("Continuing without topic extraction...\n")

    return args

def read_urls(input_file):
    """Read LinkedIn URLs from input file."""
    try:
        with open(input_file, 'r') as f:
            urls = [line.strip() for line in f if line.strip()]
        return urls
    except Exception as e:
        logger.error(f"Error reading input file: {e}")
        sys.exit(1)

def main():
    """Main function to run the LinkedIn Profile Analyzer."""
    args = parse_arguments()

    # Read LinkedIn URLs
    urls = read_urls(args.input)
    logger.info(f"Loaded {len(urls)} LinkedIn URLs from {args.input}")

    # Limit the number of profiles to visit
    if len(urls) > args.max_profiles:
        logger.warning(f"Limiting to {args.max_profiles} profiles for safety")
        urls = urls[:args.max_profiles]

    # Calculate the date threshold for recent posts
    date_threshold = datetime.now() - timedelta(days=args.days)

    # Initialize results list
    results = []

    # Initialize browser
    with LinkedInBrowser(headless=args.headless, force_visible=args.force_visible) as browser:
        # Wait for manual login
        browser.wait_for_login()

        # Process each URL
        for url in tqdm(urls, desc="Processing profiles"):
            try:
                # Visit profile
                profile_data = browser.visit_profile(url)

                if not profile_data:
                    logger.warning(f"Could not extract data from {url}")
                    continue

                # Get recent posts
                posts = browser.get_recent_posts(url, date_threshold)

                # Extract topics if requested and NLP is available
                if args.extract_topics and posts and NLP_AVAILABLE:
                    for post in posts:
                        post['topics'] = extract_topics(post['content'])
                elif args.extract_topics and posts:
                    logger.warning("Topic extraction requested but spaCy is not installed. Skipping topic extraction.")
                    for post in posts:
                        post['topics'] = []

                # Filter posts if only-shared option is enabled
                if args.only_shared:
                    filtered_posts = [post for post in posts if post.get('is_shared', False)]
                    if filtered_posts:
                        logger.info(f"Filtered to {len(filtered_posts)} shared posts out of {len(posts)} total posts")
                    else:
                        logger.info(f"No shared posts found among {len(posts)} posts")
                    posts = filtered_posts

                # Add to results
                profile_data['posts'] = posts
                results.append(profile_data)

            except Exception as e:
                logger.error(f"Error processing {url}: {e}")

    # Convert results to DataFrame
    if results:
        # Flatten the data structure for CSV output
        flat_results = []
        total_posts = 0

        for profile in results:
            if 'posts' in profile and profile['posts']:
                for post in profile['posts']:
                    # Create a dictionary with all the post data
                    post_data = {
                        'profile_url': profile['url'],
                        'name': profile['name'],
                        'title': profile['title'],
                        'company': profile.get('company', ''),
                        'location': profile.get('location', ''),
                        'post_date': post['date'],
                        'post_content': post['content'],
                        'post_url': post.get('url', ''),
                        'shared_content_url': post.get('shared_content_url', ''),
                        'post_type': post.get('type', 'text'),
                        'is_shared': post.get('is_shared', False),
                        'has_user_commentary': post.get('has_user_commentary', False),
                        'topics': ', '.join(post.get('topics', [])) if args.extract_topics else ''
                    }

                    flat_results.append(post_data)
                    total_posts += 1

                    # Print a summary of the post
                    post_summary = post['content'][:100] + "..." if len(post['content']) > 100 else post['content']
                    logger.info(f"Post from {profile['name']} on {post['date']}: {post_summary}")
            else:
                flat_results.append({
                    'profile_url': profile['url'],
                    'name': profile['name'],
                    'title': profile['title'],
                    'company': profile.get('company', ''),
                    'location': profile.get('location', ''),
                    'post_date': '',
                    'post_content': '',
                    'post_url': '',
                    'shared_content_url': '',
                    'post_type': '',
                    'is_shared': False,
                    'has_user_commentary': False,
                    'topics': ''
                })
                logger.info(f"No posts found for {profile['name']}")

        # Save to CSV
        df = pd.DataFrame(flat_results)
        df.to_csv(args.output, index=False)
        logger.info(f"Results saved to {args.output}")

        # Save full text content to a separate text file if requested
        if args.text_output:
            try:
                with open(args.text_output, 'w', encoding='utf-8') as f:
                    f.write(f"LinkedIn Posts Extracted on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"Profiles analyzed: {len(results)}\n")
                    f.write(f"Total posts found: {total_posts}\n\n")
                    f.write("="*80 + "\n\n")

                    for i, row in enumerate(flat_results):
                        if row['post_content']:
                            f.write(f"POST #{i+1}\n")
                            f.write(f"Profile: {row['name']} ({row['title']})\n")
                            f.write(f"Date: {row['post_date']}\n")
                            f.write(f"Type: {row['post_type']}")
                            if row['is_shared']:
                                f.write(" (Shared)")
                            f.write("\n\n")
                            f.write(row['post_content'])
                            f.write("\n\n")
                            f.write("-"*80 + "\n\n")

                logger.info(f"Full text content saved to {args.text_output}")
            except Exception as e:
                logger.error(f"Error saving text output: {e}")

        # Print summary
        print(f"\nProcessed {len(results)} profiles with {total_posts} posts")
        print(f"Results saved to {args.output}")
        if args.text_output:
            print(f"Full text content saved to {args.text_output}")

        # Print post type distribution if we have posts
        if total_posts > 0:
            post_types = df['post_type'].value_counts().to_dict()
            shared_count = df['is_shared'].sum()
            commentary_count = df['has_user_commentary'].sum()

            print("\nPost type distribution:")
            for post_type, count in post_types.items():
                print(f"  - {post_type}: {count} posts")
            print(f"  - Shared posts: {shared_count}")
            print(f"  - Original posts: {total_posts - shared_count}")
            print(f"  - Posts with user commentary: {commentary_count}")
    else:
        logger.warning("No results to save")

if __name__ == "__main__":
    main()
