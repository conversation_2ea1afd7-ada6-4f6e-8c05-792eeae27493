#!/usr/bin/env python3
"""
Simple LinkedIn Search - Manual approach
"""

from playwright.sync_api import sync_playwright
import time

def simple_linkedin_search():
    print("🔍 Simple LinkedIn Search")
    print("=" * 40)
    print("This will open LinkedIn and let you manually search.")
    print("Then it will try to extract the results.")
    print()
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        
        try:
            # Go to LinkedIn
            print("1. Opening LinkedIn...")
            page.goto("https://www.linkedin.com")
            
            print("2. Please login manually and then:")
            print("   - Search for \"KYC\" in the search box")
            print("   - Click on \"Posts\" filter")
            print("   - Set time filter to \"Past 24 hours\"")
            print("   - Press Enter when ready")
            
            input("Press Enter when you have completed the search and can see results...")
            
            # Now try to extract what is on the page
            print("3. Analyzing the search results page...")
            
            # Get all text content
            page_text = page.inner_text("body")
            print(f"Page contains {len(page_text)} characters")
            
            # Look for post-like content
            potential_selectors = [
                "article",
                "[data-urn]",
                ".feed-shared-update-v2",
                ".search-result",
                ".reusable-search__result",
                "div[class*=\"update\"]",
                "div[class*=\"post\"]",
                "div[class*=\"content\"]"
            ]
            
            for selector in potential_selectors:
                elements = page.query_selector_all(selector)
                if elements:
                    print(f"Found {len(elements)} elements with selector: {selector}")
                    
                    # Try to extract text from first few elements
                    for i, element in enumerate(elements[:3]):
                        try:
                            text = element.inner_text()
                            if text and len(text) > 50:
                                print(f"  Element {i+1}: {text[:100]}...")
                        except:
                            pass
            
            # Take screenshot
            page.screenshot(path="manual_search_results.png")
            print("Screenshot saved as manual_search_results.png")
            
            print("\\nKeeping browser open for inspection...")
            time.sleep(30)
            
        except Exception as e:
            print(f"Error: {e}")
        finally:
            browser.close()

if __name__ == "__main__":
    simple_linkedin_search()

