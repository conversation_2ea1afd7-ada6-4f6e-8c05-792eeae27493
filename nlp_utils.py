#!/usr/bin/env python3
"""
NLP Utilities for LinkedIn Profile Analyzer
"""

import logging
import re
from typing import List, Set

import spacy
from spacy.lang.en.stop_words import STOP_WORDS

logger = logging.getLogger(__name__)

# Load spaCy model
try:
    nlp = spacy.load("en_core_web_sm")
    logger.info("Loaded spaCy model: en_core_web_sm")
except OSError:
    logger.warning("spaCy model not found. Run: python -m spacy download en_core_web_sm")
    nlp = None

# Additional stop words specific to LinkedIn
LINKEDIN_STOP_WORDS = {
    'like', 'comment', 'share', 'post', 'see', 'more', 'linkedin',
    'profile', 'connection', 'connect', 'follow', 'following',
    'follower', 'message', 'inbox', 'notification', 'feed',
    'network', 'job', 'career', 'opportunity', 'experience',
    'skill', 'recommendation', 'endorse', 'endorsement',
    'company', 'organization', 'group', 'page', 'article',
    'publish', 'author', 'write', 'read', 'view', 'click',
    'link', 'url', 'website', 'visit', 'check', 'email',
    'contact', 'phone', 'call', 'text', 'message', 'dm',
    'direct', 'private', 'public', 'open', 'close', 'join',
    'leave', 'member', 'admin', 'moderator', 'moderate',
    'report', 'flag', 'block', 'mute', 'hide', 'show',
    'display', 'setting', 'preference', 'privacy', 'security',
    'password', 'login', 'logout', 'sign', 'account', 'profile',
    'update', 'edit', 'change', 'modify', 'delete', 'remove',
    'add', 'create', 'new', 'old', 'recent', 'latest', 'trending',
    'popular', 'top', 'best', 'worst', 'good', 'bad', 'great',
    'excellent', 'amazing', 'awesome', 'fantastic', 'wonderful',
    'terrible', 'horrible', 'awful', 'poor', 'mediocre', 'average',
    'normal', 'standard', 'typical', 'unusual', 'unique', 'rare',
    'common', 'frequent', 'occasional', 'seldom', 'never', 'always',
    'sometimes', 'often', 'usually', 'rarely', 'hardly', 'barely',
    'almost', 'nearly', 'approximately', 'exactly', 'precisely',
    'roughly', 'about', 'around', 'like', 'similar', 'different',
    'same', 'identical', 'equivalent', 'equal', 'unequal', 'unlike',
    'dissimilar', 'opposite', 'contrary', 'reverse', 'inverse'
}

# Combine with spaCy's stop words
ALL_STOP_WORDS = STOP_WORDS.union(LINKEDIN_STOP_WORDS)

def clean_text(text: str) -> str:
    """
    Clean text by removing URLs, mentions, hashtags, and special characters.
    
    Args:
        text: Input text
        
    Returns:
        Cleaned text
    """
    if not text:
        return ""
    
    # Remove URLs
    text = re.sub(r'https?://\S+|www\.\S+', '', text)
    
    # Remove mentions
    text = re.sub(r'@\w+', '', text)
    
    # Remove hashtags (keep the text, remove #)
    text = re.sub(r'#(\w+)', r'\1', text)
    
    # Remove special characters and numbers
    text = re.sub(r'[^\w\s]', ' ', text)
    text = re.sub(r'\d+', ' ', text)
    
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text).strip()
    
    return text

def extract_topics(text: str, max_topics: int = 5) -> List[str]:
    """
    Extract main topics from text using NLP.
    
    Args:
        text: Input text
        max_topics: Maximum number of topics to extract
        
    Returns:
        List of topics
    """
    if not nlp or not text:
        return []
    
    try:
        # Clean the text
        cleaned_text = clean_text(text)
        
        # Process with spaCy
        doc = nlp(cleaned_text)
        
        # Extract noun phrases and named entities
        topics = set()
        
        # Add named entities
        for ent in doc.ents:
            if ent.label_ in ('ORG', 'PRODUCT', 'WORK_OF_ART', 'EVENT', 'NORP', 'FAC', 'GPE'):
                topics.add(ent.text.lower())
        
        # Add noun chunks (noun phrases)
        for chunk in doc.noun_chunks:
            # Filter out stop words and short phrases
            if (len(chunk.text.split()) > 1 and 
                not all(token.is_stop for token in chunk) and
                len(chunk.text) > 3):
                topics.add(chunk.text.lower())
        
        # Add important single nouns
        for token in doc:
            if (token.pos_ == 'NOUN' and 
                not token.is_stop and 
                token.text.lower() not in ALL_STOP_WORDS and
                len(token.text) > 3):
                topics.add(token.text.lower())
        
        # Sort by length (prefer longer phrases) and limit
        return sorted(list(topics), key=len, reverse=True)[:max_topics]
        
    except Exception as e:
        logger.error(f"Error extracting topics: {e}")
        return []

def extract_keywords(text: str, max_keywords: int = 10) -> List[str]:
    """
    Extract keywords from text.
    
    Args:
        text: Input text
        max_keywords: Maximum number of keywords to extract
        
    Returns:
        List of keywords
    """
    if not nlp or not text:
        return []
    
    try:
        # Clean the text
        cleaned_text = clean_text(text)
        
        # Process with spaCy
        doc = nlp(cleaned_text)
        
        # Extract keywords (nouns, proper nouns, adjectives)
        keywords = {}
        
        for token in doc:
            if (token.pos_ in ('NOUN', 'PROPN', 'ADJ') and 
                not token.is_stop and 
                token.text.lower() not in ALL_STOP_WORDS and
                len(token.text) > 2):
                
                # Use lemma to group similar words
                lemma = token.lemma_.lower()
                
                if lemma in keywords:
                    keywords[lemma] += 1
                else:
                    keywords[lemma] = 1
        
        # Sort by frequency and limit
        sorted_keywords = sorted(keywords.items(), key=lambda x: x[1], reverse=True)
        return [k for k, v in sorted_keywords[:max_keywords]]
        
    except Exception as e:
        logger.error(f"Error extracting keywords: {e}")
        return []

def match_keywords(text: str, keywords: List[str]) -> List[str]:
    """
    Match keywords in text.
    
    Args:
        text: Input text
        keywords: List of keywords to match
        
    Returns:
        List of matched keywords
    """
    if not text or not keywords:
        return []
    
    try:
        # Clean the text
        cleaned_text = clean_text(text).lower()
        
        # Match keywords
        matched = set()
        
        for keyword in keywords:
            if keyword.lower() in cleaned_text:
                matched.add(keyword)
        
        return list(matched)
        
    except Exception as e:
        logger.error(f"Error matching keywords: {e}")
        return []
