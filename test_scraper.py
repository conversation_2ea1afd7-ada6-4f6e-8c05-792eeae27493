#!/usr/bin/env python3
"""
Test script for LinkedIn Keyword Scraper
"""

import logging
import sys
from linkedin_keyword_scraper import LinkedInKeywordScraper

def test_basic_functionality():
    """Test basic scraper functionality."""
    print("Testing LinkedIn Keyword Scraper...")

    # Set up logging
    logging.basicConfig(level=logging.INFO)

    # Test keywords
    test_keywords = ["python", "artificial intelligence"]

    try:
        print(f"Testing with keywords: {test_keywords}")
        print("Running in visible mode for testing...")

        with LinkedInKeywordScraper(headless=False, max_posts=5) as scraper:
            posts = scraper.search_posts_by_keywords(test_keywords, hours_back=24)

            print(f"\nTest Results:")
            print(f"- Keywords searched: {test_keywords}")
            print(f"- Posts found: {len(posts)}")

            if posts:
                print(f"\nSample post:")
                sample_post = posts[0]
                print(f"- Author: {sample_post.get('author_name', 'Unknown')}")
                print(f"- Date: {sample_post.get('date', 'Unknown')}")
                print(f"- Content preview: {sample_post.get('content', '')[:100]}...")
                print(f"- URL: {sample_post.get('url', 'No URL')}")
            else:
                print("No posts found. This could be due to:")
                print("- LinkedIn blocking automated access")
                print("- No recent posts with the test keywords")
                print("- Network connectivity issues")

            return len(posts) > 0

    except Exception as e:
        print(f"Test failed with error: {e}")
        return False

def test_date_parsing():
    """Test date parsing functionality."""
    print("\nTesting date parsing...")

    scraper = LinkedInKeywordScraper()

    test_dates = [
        "2 hours ago",
        "1 day ago",
        "3 weeks ago",
        "just now",
        "yesterday"
    ]

    for date_str in test_dates:
        parsed_date = scraper._parse_linkedin_date(date_str)
        print(f"'{date_str}' -> {parsed_date}")

    print("Date parsing test completed.")

if __name__ == "__main__":
    print("LinkedIn Keyword Scraper Test Suite")
    print("=" * 50)

    # Test date parsing (doesn't require browser)
    test_date_parsing()

    # Test basic functionality (requires browser)
    print("\nStarting browser test...")
    print("Note: This will open a browser window for testing.")

    user_input = input("Continue with browser test? (y/n): ").lower().strip()

    if user_input == 'y':
        success = test_basic_functionality()
        if success:
            print("\n✅ Test completed successfully!")
        else:
            print("\n❌ Test failed!")
            sys.exit(1)
    else:
        print("Browser test skipped.")

    print("\nTest suite completed.")
