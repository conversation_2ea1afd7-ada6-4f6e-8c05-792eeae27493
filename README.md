# LinkedIn Keyword Scraper

A safe LinkedIn scraper that searches for posts containing specific keywords within the last 24 hours (or custom time range). Supports both authenticated (recommended) and public access modes with comprehensive safety measures to protect your LinkedIn account.

## Features

- **🔐 Safe Login Support**: Manual login for enhanced data access with maximum account protection
- **🌐 No-Login Mode**: Alternative public content access (limited results)
- **🔍 Keyword-Based Search**: Search for posts containing specific keywords or phrases
- **⏰ Time Filtering**: Filter posts by time range (default: last 24 hours)
- **🛡️ Advanced Safety Measures**: Comprehensive protection against account flagging
- **📊 Multiple Output Formats**: Export results to CSV or JSON
- **🔄 Duplicate Detection**: Automatically removes duplicate posts
- **⚙️ Highly Configurable**: Customizable search parameters and safety settings

## Safety Features

### Account Protection (Login Mode)
- **Human-like Delays**: 5-15 second delays between actions
- **Limited Search Volume**: Maximum 10 searches per session
- **Browsing Simulation**: Randomly visits other LinkedIn pages
- **Reading Time Simulation**: Mimics natural reading patterns
- **Enhanced Error Handling**: Graceful recovery from rate limits
- **Session Monitoring**: Tracks activity to prevent overuse

## Installation

1. Clone or download this repository
2. Install Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Install Playwright browsers:
   ```bash
   playwright install
   ```

## Usage

### Basic Usage (Recommended - Login Mode)

Search for posts with manual login for best results:
```bash
python keyword_search.py --keywords "artificial intelligence" "machine learning"
```

### Advanced Usage

```bash
# Search with custom time range and output format (login mode)
python keyword_search.py -k "python developer" -k "remote work" --hours 48 --format json --output results.json

# Use keywords from a file with maximum safety
python keyword_search.py --keywords-file keywords.txt --max-posts 15

# No-login mode (limited results)
python keyword_search.py --keywords "data science" --no-login

# Headless mode (not recommended for login)
python keyword_search.py --keywords "AI" --headless --debug
```

### Command Line Options

- `--keywords, -k`: Keywords to search for (space-separated)
- `--keywords-file, -f`: File containing keywords (one per line)
- `--hours, -h`: Number of hours to look back (default: 24)
- `--output, -o`: Output file path (default: linkedin_keyword_results.csv)
- `--format`: Output format - csv or json (default: csv)
- `--max-posts`: Maximum posts per keyword (default: 25, reduced for safety)
- `--no-login`: Skip login and use limited public access (not recommended)
- `--headless`: Run browser in headless mode (not recommended for login)
- `--debug`: Enable debug logging

## Output Format

### CSV Output
The CSV file contains the following columns:
- `keyword`: The keyword that matched this post
- `author_name`: Name of the post author
- `author_title`: Professional title/headline of the author
- `date`: Post publication date (ISO format)
- `content`: Full text content of the post
- `url`: Direct link to the LinkedIn post
- `extracted_at`: Timestamp when the post was scraped

### JSON Output
The JSON file contains an array of post objects with the same fields as the CSV format.

## Safety Features

This scraper implements several safety measures to avoid detection:

1. **Human-like Browsing**: Random delays between actions, realistic scrolling patterns
2. **Anti-Detection Scripts**: Overrides automation detection properties
3. **Request Throttling**: Configurable delays between searches
4. **Realistic User Agent**: Uses standard browser user agents
5. **No Login Required**: Avoids the risk of account flagging

## Examples

### Example 1: Tech Job Search
```bash
python keyword_search.py --keywords "software engineer" "python developer" "remote work" --hours 24 --output tech_jobs.csv
```

### Example 2: Industry Trends
```bash
python keyword_search.py -k "artificial intelligence" -k "blockchain" -k "cryptocurrency" --hours 72 --format json --output trends.json
```

### Example 3: Using Keywords File
Create a file `keywords.txt`:
```
machine learning
data science
python programming
artificial intelligence
deep learning
```

Then run:
```bash
python keyword_search.py --keywords-file keywords.txt --max-posts 25 --output ml_posts.csv
```

## Limitations

- **Public Content Only**: Can only access publicly visible LinkedIn posts
- **Rate Limiting**: LinkedIn may impose rate limits on frequent requests
- **Content Availability**: Some posts may not be accessible without login
- **Dynamic Content**: LinkedIn frequently changes their HTML structure, which may require updates

## Troubleshooting

### Common Issues

1. **No posts found**: Try different keywords or increase the time range
2. **Browser not launching**: Install Playwright browsers with `playwright install`
3. **Rate limiting**: Reduce the number of keywords or increase delays between searches
4. **Empty results**: LinkedIn may be blocking automated access - try running in visible mode

### Debug Mode

Run with `--debug` flag to see detailed logging:
```bash
python keyword_search.py --keywords "test" --debug --visible
```

## Legal and Ethical Considerations

- This tool is for educational and research purposes only
- Respect LinkedIn's Terms of Service and robots.txt
- Use responsibly and avoid overwhelming LinkedIn's servers
- Consider the privacy of users whose posts you're collecting
- Always comply with applicable data protection laws (GDPR, CCPA, etc.)

## Contributing

Feel free to submit issues, feature requests, or pull requests to improve this tool.

## License

This project is provided as-is for educational purposes. Use at your own risk and responsibility.
