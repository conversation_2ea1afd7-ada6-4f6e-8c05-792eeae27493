# LinkedIn Profile Analyzer

A Python tool for safely analyzing LinkedIn profiles and posts based on job titles and keywords. This tool is designed for personal use only and implements multiple safety measures to avoid getting your LinkedIn account flagged or restricted.

## Features

- Browser automation approach (using <PERSON>wright)
- Manual login required (no username/password storage)
- Human-like browsing behavior (random delays, throttling, gentle scrolling)
- Profile visit limits to stay within LinkedIn's acceptable usage
- Recent post extraction (configurable time period, default 14 days)
- **Focused content extraction**: Only extracts posts where the individual has actively participated:
  - Original posts authored by the individual
  - Shared/reposted content where the individual added their own commentary
- Comprehensive text extraction from qualifying posts
- Distinguishes between different post types (text, article, image, video, etc.)
- Extracts URLs for both the individual's post and any shared content
- CSV output for easy analysis
- Optional plain text output for full post content
- Optional topic extraction using NLP (requires spaCy installation)

## Safety Measures

This tool implements several safety measures to protect your LinkedIn account:

1. **Manual Login**: You must log in manually to your LinkedIn account. No credentials are stored.
2. **Human-like Browsing**: Random delays between actions, natural scrolling behavior, and occasional pauses.
3. **Request Throttling**: Limited number of requests per session.
4. **Browser Fingerprint Randomization**: Helps avoid detection as an automated tool.
5. **Session Limits**: Configurable maximum number of profiles to visit per session.

## Installation

1. Clone this repository:
   ```
   git clone <repository-url>
   cd linkedin-profile-analyzer
   ```

2. Create a virtual environment and activate it:
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install required dependencies:
   ```
   pip install -r requirements.txt
   ```

4. Install Playwright browsers:
   ```
   playwright install
   ```

5. (Optional) For NLP features, install spaCy:
   ```
   pip install spacy
   python -m spacy download en_core_web_sm
   ```

## Usage

### Basic Usage

1. Create a text file with LinkedIn profile URLs (one per line):
   ```
   https://www.linkedin.com/in/profile1/
   https://www.linkedin.com/in/profile2/
   https://www.linkedin.com/in/profile3/
   ```

2. Run the analyzer:
   ```
   python main.py --input profiles.txt --output results.csv
   ```

3. When prompted, manually log in to your LinkedIn account in the browser window.

4. The tool will process the profiles and save the results to the specified CSV file.

### Command-Line Options

```
python main.py --help
```

Available options:

- `--input`, `-i`: Path to file containing LinkedIn URLs (one per line) [required]
- `--output`, `-o`: Path to output CSV file [default: linkedin_results.csv]
- `--text-output`: Path to output text file for full post content [optional]
- `--days`, `-d`: Number of days to look back for recent posts [default: 14]
- `--max-profiles`, `-m`: Maximum number of profiles to visit in this session [default: 30]
- `--extract-topics`, `-t`: Extract topics from posts using NLP (requires spaCy) [flag]
- `--headless`: Run browser in headless mode (not recommended for login) [flag]
- `--force-visible`: Force the browser to be visible (use if browser window doesn't appear) [flag]
- `--debug`: Enable debug logging for troubleshooting [flag]
- `--only-shared`: Only extract shared posts (with user commentary) [flag]

## Examples

Basic usage:
```
python main.py --input profiles.txt --output results.csv
```

With topic extraction (requires spaCy):
```
python main.py --input profiles.txt --output results.csv --extract-topics
```

With custom parameters:
```
python main.py --input profiles.txt --output results.csv --days 7 --max-profiles 20
```

If browser window doesn't appear:
```
python main.py --input profiles.txt --output results.csv --force-visible
```

For troubleshooting:
```
python main.py --input profiles.txt --output results.csv --debug
```

Extract only shared posts with user commentary:
```
python main.py --input profiles.txt --output results.csv --only-shared
```

Save full text content to a separate file:
```
python main.py --input profiles.txt --output results.csv --text-output posts.txt
```

Each example will:
- Process LinkedIn profiles from the input file
- Extract recent posts based on the specified time period
- Save the results to a CSV file for analysis

## Output Format

### CSV Output

The output CSV file contains the following columns:

- `profile_url`: LinkedIn profile URL
- `name`: Profile name
- `title`: Job title
- `company`: Company name
- `location`: Profile location
- `post_date`: Date of the post
- `post_content`: Full text content of the post (includes user commentary for shared posts)
- `post_url`: URL to the individual's specific post (when available)
- `shared_content_url`: URL to the original shared content (when applicable)
- `post_type`: Type of post (text, article, image, video, document, poll)
- `is_shared`: Whether the post is shared/reposted content (boolean)
- `has_user_commentary`: Whether the user added commentary to shared content (boolean)
- `topics`: Extracted topics (if enabled)

### Text Output

If you specify the `--text-output` option, a plain text file will be created with:

- Summary information (extraction date, profiles analyzed, total posts)
- Each post formatted with:
  - Post number
  - Profile information
  - Post date
  - Post type
  - Full post content
  - Separator between posts

## Staying Within LinkedIn's Acceptable Usage

To avoid getting your account flagged or restricted:

1. **Limit your sessions**: Don't run the tool for extended periods.
2. **Respect the max-profiles limit**: 30-50 profiles per day is generally safe.
3. **Use your regular browser**: Run the tool on the same device and browser you normally use for LinkedIn.
4. **Don't run 24/7**: Use the tool occasionally, not continuously.
5. **Be a regular user**: Continue to use LinkedIn normally alongside this tool.

## Extending the Tool

The code is modular and can be extended in several ways:

- Add a database backend to store results
- Create a dashboard for visualizing the data
- Implement more advanced NLP for deeper content analysis
- Add filtering capabilities based on keywords or job titles

## Troubleshooting

If you encounter any issues, please check the following:

### Browser Doesn't Open

If the browser window doesn't appear:

1. Try running with the `--force-visible` flag:
   ```
   python main.py --input profiles.txt --output results.csv --force-visible
   ```

2. Make sure you have a compatible browser installed (Chrome, Chromium, or Firefox)

3. Check if Playwright browsers are installed correctly:
   ```
   python -m playwright install
   ```

### No Posts Found

If the tool is not finding any posts:

1. Try running with the `--debug` flag to see detailed logs:
   ```
   python main.py --input profiles.txt --output results.csv --debug
   ```

2. Make sure the profiles you're analyzing have public posts

3. Try increasing the number of days to look back:
   ```
   python main.py --input profiles.txt --output results.csv --days 30
   ```

4. Check if LinkedIn has changed their HTML structure (the tool tries multiple selectors but may need updates)

### Other Common Issues

1. Make sure you have installed all the required dependencies.
2. Check the log file (`linkedin_analyzer.log`) for detailed error messages.
3. If you're getting rate-limited by LinkedIn, try reducing the `--max-profiles` value.
4. Make sure your LinkedIn account is in good standing and not already restricted.
5. If you're having issues with topic extraction, make sure spaCy is installed correctly.
6. Try running with different browsers by modifying the code in `linkedin_browser.py`.

## Disclaimer

This tool is for personal use only. Use responsibly and in accordance with LinkedIn's Terms of Service. The authors are not responsible for any account restrictions or other consequences resulting from the use of this tool.
