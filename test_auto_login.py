#!/usr/bin/env python3
"""
Quick test for LinkedIn auto-login
"""

import getpass
from linkedin_keyword_scraper import LinkedInKeywordScraper

def test_auto_login():
    print("LinkedIn Auto-Login Test")
    print("=" * 30)
    
    email = input("Enter your LinkedIn email: ")
    password = getpass.getpass("Enter your LinkedIn password: ")
    
    print("\nTesting auto-login...")
    
    try:
        with LinkedInKeywordScraper(
            headless=False,
            max_posts=5,
            require_login=True,
            email=email,
            password=password
        ) as scraper:
            print("✅ Browser started successfully")
            
            # Test a simple search
            posts = scraper.search_posts_by_keywords(["test"], hours_back=24)
            print(f"✅ Search completed. Found {len(posts)} posts")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_auto_login()

