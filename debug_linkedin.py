#!/usr/bin/env python3
"""
LinkedIn Debug Script - Shows exactly what is on the page
"""

import time
from playwright.sync_api import sync_playwright

def debug_linkedin_search():
    print("🔍 LinkedIn Search Debug Tool")
    print("=" * 50)

    with sync_playwright() as p:
        # Launch browser in visible mode
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()

        try:
            # Navigate to LinkedIn
            print("1. Navigating to LinkedIn...")
            page.goto("https://www.linkedin.com")
            time.sleep(3)

            # Check if we need to login
            current_url = page.url
            print(f"Current URL: {current_url}")

            if "login" in current_url or page.query_selector("#username"):
                print("2. Login required - please login manually in the browser")
                print("   Waiting 60 seconds for you to complete login...")
                time.sleep(60)

            # Navigate to KYC search
            search_url = "https://www.linkedin.com/search/results/content/?datePosted=%22past-24h%22&keywords=KYC&origin=FACETED_SEARCH&sortBy=%22date_posted%22"
            print(f"3. Navigating to search: {search_url}")
            page.goto(search_url)
            time.sleep(5)

            # Debug: Check what is on the page
            print("4. Analyzing page content...")

            # Check page title
            title = page.title()
            print(f"Page title: {title}")

            # Check for common elements
            selectors_to_check = [
                ".search-results-container",
                ".search-result",
                ".reusable-search__result",
                ".search-no-results",
                ".search-results__list",
                "div[data-chameleon-result-urn]",
                ".feed-shared-update-v2"
            ]

            found_elements = {}
            for selector in selectors_to_check:
                elements = page.query_selector_all(selector)
                found_elements[selector] = len(elements)
                if elements:
                    print(f"✅ Found {len(elements)} elements with: {selector}")
                else:
                    print(f"❌ No elements found with: {selector}")

            # Get page text content
            body_text = page.inner_text("body")
            print(f"\\nPage content length: {len(body_text)} characters")

            # Check for specific keywords in page
            keywords_to_check = ["KYC", "search", "results", "posts", "no results", "sign in", "login"]
            for keyword in keywords_to_check:
                if keyword.lower() in body_text.lower():
                    print(f"✅ Found keyword \"{keyword}\" in page content")
                else:
                    print(f"❌ Keyword \"{keyword}\" not found in page content")

            # Show a sample of page content
            print(f"\\nFirst 500 characters of page:")
            print("-" * 50)
            print(body_text[:500])
            print("-" * 50)

            # Take a screenshot
            screenshot_path = "linkedin_debug.png"
            page.screenshot(path=screenshot_path)
            print(f"\\n📸 Screenshot saved as: {screenshot_path}")

            print("\\n5. Keeping browser open for 30 seconds for manual inspection...")
            time.sleep(30)

        except Exception as e:
            print(f"❌ Error: {e}")
        finally:
            browser.close()

if __name__ == "__main__":
    debug_linkedin_search()

