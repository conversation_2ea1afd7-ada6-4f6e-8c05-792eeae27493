#!/bin/bash
# Setup script for LinkedIn Keyword Scraper

echo "Setting up LinkedIn Keyword Scraper..."

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is not installed. Please install Python 3.8 or later."
    exit 1
fi

echo "Python 3 found: $(python3 --version)"

# Install Python dependencies
echo "Installing Python dependencies..."
pip3 install -r requirements.txt

if [ $? -ne 0 ]; then
    echo "Error: Failed to install Python dependencies."
    exit 1
fi

# Install Playwright browsers
echo "Installing Playwright browsers..."
playwright install

if [ $? -ne 0 ]; then
    echo "Error: Failed to install Playwright browsers."
    exit 1
fi

echo "Setup completed successfully!"
echo ""
echo "You can now run the scraper with:"
echo "python3 keyword_search.py --keywords \"your keyword\" \"another keyword\""
echo ""
echo "For help, run:"
echo "python3 keyword_search.py --help"
echo ""
echo "To test the installation, run:"
echo "python3 test_scraper.py"
